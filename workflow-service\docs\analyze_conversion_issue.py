"""
Analyze the workflow schema conversion issue with the provided workflow_builder_schema.json
"""

import json
from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema

def analyze_conversion_issue():
    """Analyze the specific conversion issue with the provided schema."""

    # Load the problematic workflow schema
    with open("../worklow_builder_schema.json", "r") as f:
        workflow_data = json.load(f)

    print("🔍 ANALYZING WORKFLOW SCHEMA CONVERSION ISSUE")
    print("=" * 60)

    # Extract workflow data from the nested structure
    actual_workflow_data = workflow_data["workflow_data"]

    print(f"\n📋 SOURCE WORKFLOW ANALYSIS:")
    print(f"  Total nodes: {len(actual_workflow_data['nodes'])}")
    print(f"  Total edges: {len(actual_workflow_data['edges'])}")

    # Analyze nodes
    print(f"\n🔍 NODE ANALYSIS:")
    for i, node in enumerate(actual_workflow_data['nodes']):
        node_type = node.get('data', {}).get('type', 'Unknown')
        original_type = node.get('data', {}).get('originalType', 'Unknown')
        print(f"  {i+1}. {node['id']} - Type: {node_type}, Original: {original_type}")

    # Analyze edges - focus on conditional node edges
    print(f"\n🔗 EDGE ANALYSIS (Conditional Node Focus):")
    conditional_edges = []
    for edge in actual_workflow_data['edges']:
        if 'ConditionalNode' in edge.get('source', '') or 'ConditionalNode' in edge.get('target', ''):
            conditional_edges.append(edge)
            source_handle = edge.get('sourceHandle', 'NO_HANDLE')
            target_handle = edge.get('targetHandle', 'NO_HANDLE')
            print(f"  {edge['id']}")
            print(f"    Source: {edge['source']} ({source_handle})")
            print(f"    Target: {edge['target']} ({target_handle})")

    # Analyze conditional node configuration
    conditional_node = None
    for node in actual_workflow_data['nodes']:
        if node.get('data', {}).get('originalType') == 'ConditionalNode':
            conditional_node = node
            break

    if conditional_node:
        print(f"\n🎯 CONDITIONAL NODE CONFIGURATION:")
        definition = conditional_node.get('data', {}).get('definition', {})

        # Check num_additional_conditions
        num_additional = None
        for input_def in definition.get('inputs', []):
            if input_def['name'] == 'num_additional_conditions':
                num_additional = input_def.get('value', 0)
                break

        print(f"  Node ID: {conditional_node['id']}")
        print(f"  Additional Conditions: {num_additional}")

        # Check condition configurations
        print(f"  Condition Configurations:")
        for i in range(1, 3):  # Base conditions
            source = None
            operator = None
            expected_value = None

            for input_def in definition.get('inputs', []):
                if input_def['name'] == f'condition_{i}_source':
                    source = input_def.get('value')
                elif input_def['name'] == f'condition_{i}_operator':
                    operator = input_def.get('value')
                elif input_def['name'] == f'condition_{i}_expected_value':
                    expected_value = input_def.get('value')

            print(f"    Condition {i}: {source} {operator} '{expected_value}'")

    print(f"\n🔄 ATTEMPTING CONVERSION...")
    try:
        # Convert to transition schema
        transition_schema = convert_workflow_to_transition_schema(actual_workflow_data)

        print(f"\n✅ CONVERSION SUCCESSFUL!")
        print(f"  Nodes in transition schema: {len(transition_schema['nodes'])}")
        print(f"  Transitions in transition schema: {len(transition_schema['transitions'])}")

        # Check if conditional node appears in nodes array
        conditional_in_nodes = any("Conditional" in node['id'] for node in transition_schema['nodes'])
        print(f"  Conditional node in nodes array: {conditional_in_nodes}")

        # Check for conditional routing in transitions
        transitions_with_conditional = []
        for transition in transition_schema['transitions']:
            if 'conditional_routing' in transition:
                transitions_with_conditional.append(transition['id'])
                cases = transition['conditional_routing'].get('cases', [])
                print(f"  {transition['id']} has conditional routing with {len(cases)} cases")

        if not transitions_with_conditional:
            print(f"  ❌ NO CONDITIONAL ROUTING FOUND in any transitions")

        # Check for separate conditional transitions
        conditional_transitions = [t for t in transition_schema['transitions'] if 'Conditional' in t['id']]
        if conditional_transitions:
            print(f"  ❌ PROBLEM: Found {len(conditional_transitions)} separate conditional transitions:")
            for ct in conditional_transitions:
                print(f"    - {ct['id']}")
        else:
            print(f"  ✅ GOOD: No separate conditional transitions found")

        # Save the converted schema for inspection
        with open("converted_transition_schema.json", "w") as f:
            json.dump(transition_schema, f, indent=2)
        print(f"\n💾 Converted schema saved to converted_transition_schema.json")

        return transition_schema

    except Exception as e:
        print(f"\n❌ CONVERSION FAILED!")
        print(f"  Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_conversion_issue()
