2025-05-29 16:53:29 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-29\ToolExecutor_16-53-29.log
2025-05-29 16:53:29 - ToolExecutor - INFO - [setup_tool_executor_logger:97] <PERSON><PERSON><PERSON> logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-29 16:53:29 - ToolExecutor - INFO - [get_tool_executor:281] Creating new global ToolExecutor instance
2025-05-29 16:53:29 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-29 16:53:29 - ToolExecutor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-29 17:01:54 - ToolExecutor - INFO - [execute_tool:94] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Executing tool for request_id: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:54 - ToolExecutor - INFO - [execute_tool:97] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "4156040d-38a6-49db-b202-d383397630ea",
  "correlation_id": "b85e16bc-2757-44fe-a6b3-e0f4e3a55c01"
}
2025-05-29 17:01:54 - ToolExecutor - INFO - [execute_tool:116] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Tool name: ApiRequestNode for request_id: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:54 - ToolExecutor - INFO - [execute_tool:151] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Processing payload with component ApiRequestNode for request_id: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:94] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Executing tool for request_id: a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:97] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": null,
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "a75efa9f-6545-4261-ac84-aba74622d7ea",
  "correlation_id": "b85e16bc-2757-44fe-a6b3-e0f4e3a55c01"
}
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:116] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Tool name: MergeDataComponent for request_id: a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:151] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Processing payload with component MergeDataComponent for request_id: a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:155] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Component MergeDataComponent processed payload successfully for request_id: a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:186] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] ToolExecutor returning error from component: {
  "request_id": "a75efa9f-6545-4261-ac84-aba74622d7ea",
  "status": "error",
  "result": {
    "error": "Unknown merge strategy for request_id a75efa9f-6545-4261-ac84-aba74622d7ea: None"
  }
}
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:155] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Component ApiRequestNode processed payload successfully for request_id: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ToolExecutor - INFO - [execute_tool:225] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "4156040d-38a6-49db-b202-d383397630ea",
  "status": "success",
  "response": {
    "result": "1748518315543-9699916734825",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 11:31:55 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-Coxo3/byphWYxA+8pyAdVjMvcpc\"",
      "X-Response-Time": "0.51569ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=kgIGji3HQ3OUxQwMHeLB1jVyShxCQC8pBlXpCBek5gPKJkCeM36QwU5%2BxlcRy0z5uP2V3mxIXbdGbbx1FPfA0Kkgq9lpb%2B%2FT96XB2cbAxYxjq2Kdi1o%2FEA%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475ab0e6b4fe161-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST"
  }
}
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:94] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool for request_id: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:97] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "eb7c4840-2e2c-45ff-8b32-5f289a5167ce",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:116] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool name: ApiRequestNode for request_id: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:151] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing payload with component ApiRequestNode for request_id: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:94] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool for request_id: 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:97] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "20dedc4d-6b19-4a22-86fe-a73e762633c9",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:116] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool name: MergeDataComponent for request_id: 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:151] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing payload with component MergeDataComponent for request_id: 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:155] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Component MergeDataComponent processed payload successfully for request_id: 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - ToolExecutor - INFO - [execute_tool:225] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor returning success: {
  "request_id": "20dedc4d-6b19-4a22-86fe-a73e762633c9",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new\":\"112\"}",
      "social": "5"
    }
  }
}
2025-05-29 17:04:09 - ToolExecutor - INFO - [execute_tool:155] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Component ApiRequestNode processed payload successfully for request_id: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ToolExecutor - INFO - [execute_tool:225] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "eb7c4840-2e2c-45ff-8b32-5f289a5167ce",
  "status": "success",
  "response": {
    "result": "1748518449180-7919062976725",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 11:34:09 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-Wg55KgkBTPfolaGIcnCfH2ztqnA\"",
      "X-Response-Time": "0.63825ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=uBNcG%2FLovATJkOsVTYy2OuEbAwUh%2BGQoO22F728W9XzbH8B3FWZrUVGEwmnvKOpRP%2BrHQ5GjS%2F7nsK5JWz6jrygUEUR7F3PrfsAD6eC71PoM1rRBiP20VQ%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475ae519b08e1fd-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST"
  }
}
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:94] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool for request_id: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:97] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "ec2122d4-c3b0-4386-a592-683439438d95",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:116] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool name: ApiRequestNode for request_id: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:151] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing payload with component ApiRequestNode for request_id: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:94] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool for request_id: 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:97] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "53adf93a-c760-48fc-a088-529088348acd",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:116] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool name: MergeDataComponent for request_id: 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:151] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing payload with component MergeDataComponent for request_id: 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:155] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Component MergeDataComponent processed payload successfully for request_id: 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:225] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor returning success: {
  "request_id": "53adf93a-c760-48fc-a088-529088348acd",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new\":\"112\"}",
      "media": "help"
    }
  }
}
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:155] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Component ApiRequestNode processed payload successfully for request_id: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ToolExecutor - INFO - [execute_tool:225] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "ec2122d4-c3b0-4386-a592-683439438d95",
  "status": "success",
  "response": {
    "result": "1748518458479-5113869060296",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 11:34:18 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-l8lKaC21Vesm5oba7Fk4dcWQqZs\"",
      "X-Response-Time": "0.49125ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=80qxtokT5vxAS3oOT7hk4m6n%2FDB4pKcIZ5RCWXkPBXPi1FLqnnoafjI1TqcuW8UaJKRfq%2F%2FzmE3V9nvcTb%2BmydabYu80Zm1c8SAW6%2BnLMi85fw11pgYOXQ%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475ae8bce3ce17b-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST"
  }
}
2025-05-29 17:10:15 - ToolExecutor - INFO - [execute_tool:94] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool for request_id: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:15 - ToolExecutor - INFO - [execute_tool:97] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "315f43ec-5f25-47ca-a36e-9d853e41b475",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:15 - ToolExecutor - INFO - [execute_tool:116] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool name: ApiRequestNode for request_id: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:15 - ToolExecutor - INFO - [execute_tool:151] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing payload with component ApiRequestNode for request_id: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:94] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool for request_id: 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:97] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:116] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool name: MergeDataComponent for request_id: 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:151] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing payload with component MergeDataComponent for request_id: 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:155] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Component MergeDataComponent processed payload successfully for request_id: 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:225] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor returning success: {
  "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new\":\"112\"}",
      "social": "5"
    }
  }
}
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:155] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Component ApiRequestNode processed payload successfully for request_id: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ToolExecutor - INFO - [execute_tool:225] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "315f43ec-5f25-47ca-a36e-9d853e41b475",
  "status": "success",
  "response": {
    "result": "1748518816428-6256029747892",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 11:40:16 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-9+pwHn8MwsTiIIwmpJHaq4Qqnu8\"",
      "X-Response-Time": "0.62231ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=bIAJGO5z6IQy6aaR9Bt%2B3QZNJ%2BDi0ayJ9N%2BeYMElG5IDxKWccdOE11qbhw8yonwrLJesUofa5sJnXYSWxLuaIxx8%2FXkPfK0Eq9fclQ5bFMox4rfqYONTtg%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475b748f9f7e1cc-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST"
  }
}
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:94] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool for request_id: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:97] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "bdcfd532-d649-4cde-a293-019db805555a",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:116] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool name: ApiRequestNode for request_id: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:151] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing payload with component ApiRequestNode for request_id: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:94] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool for request_id: 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:97] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "4fbc78b5-9b1b-4357-ab42-407864302c0d",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:116] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool name: MergeDataComponent for request_id: 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:151] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing payload with component MergeDataComponent for request_id: 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:155] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Component MergeDataComponent processed payload successfully for request_id: 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:225] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor returning success: {
  "request_id": "4fbc78b5-9b1b-4357-ab42-407864302c0d",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new\":\"112\"}",
      "media": "help"
    }
  }
}
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:155] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Component ApiRequestNode processed payload successfully for request_id: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ToolExecutor - INFO - [execute_tool:225] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "bdcfd532-d649-4cde-a293-019db805555a",
  "status": "success",
  "response": {
    "result": "1748518824170-5158934467472",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 11:40:24 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-bIGiOAHo1yGDshmmJ0AJQyzz4Y8\"",
      "X-Response-Time": "0.57367ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=mcdgNi0rHEG%2F2JUjGkLnmVOFFbU9W0Srwmp9eKayQRDH0WU3l%2FX91hvIWSudPjrqVyg7CBvQw2T3Ns5oIK4LLifFZlvtB%2FvMJ7KKOQmk9FtAPPxBo%2BSPVA%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475b77a998be1cd-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST"
  }
}
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:94] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Executing tool for request_id: be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:97] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "be6ecb6e-1c4a-4d66-9017-5852c2efc0d3",
  "correlation_id": "c64cbedd-a7f5-4f92-a473-f0ca789ecb0b"
}
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:116] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Tool name: MergeDataComponent for request_id: be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:151] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Processing payload with component MergeDataComponent for request_id: be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:155] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Component MergeDataComponent processed payload successfully for request_id: be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:225] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] ToolExecutor returning success: {
  "request_id": "be6ecb6e-1c4a-4d66-9017-5852c2efc0d3",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new_value\":\"112\"}",
      "social": "5"
    }
  }
}
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:94] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Executing tool for request_id: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:97] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "15c42e55-1b6b-4004-8650-5222caa8cabe",
  "correlation_id": "c64cbedd-a7f5-4f92-a473-f0ca789ecb0b"
}
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:116] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Tool name: ApiRequestNode for request_id: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ToolExecutor - INFO - [execute_tool:151] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Processing payload with component ApiRequestNode for request_id: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ToolExecutor - INFO - [execute_tool:155] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Component ApiRequestNode processed payload successfully for request_id: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ToolExecutor - INFO - [execute_tool:186] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] ToolExecutor returning error from component: {
  "component_type": "ApiRequestNode",
  "request_id": "15c42e55-1b6b-4004-8650-5222caa8cabe",
  "status": "error",
  "response": {
    "result": "404 - Not Found\n",
    "status_code": 404,
    "response_headers": {},
    "error": "API request failed with status 404 (Not Found): 404 - Not Found\n"
  }
}
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool for request_id: c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "c3220f92-eb78-49a0-afca-1353ca12850b",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:116] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool name: MergeDataComponent for request_id: c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:151] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing payload with component MergeDataComponent for request_id: c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:155] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Component MergeDataComponent processed payload successfully for request_id: c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:225] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor returning success: {
  "request_id": "c3220f92-eb78-49a0-afca-1353ca12850b",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new_value\":\"112\"}",
      "social": "5"
    }
  }
}
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool for request_id: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "45369c3d-1882-4678-bffc-f457c37b70e9",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:116] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool name: ApiRequestNode for request_id: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:151] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing payload with component ApiRequestNode for request_id: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:155] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Component ApiRequestNode processed payload successfully for request_id: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ToolExecutor - INFO - [execute_tool:225] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "45369c3d-1882-4678-bffc-f457c37b70e9",
  "status": "success",
  "response": {
    "result": "1748520314529-8490416419226",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:05:14 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-g0TpDRY4gUYKDzMQrfXxGAASrvM\"",
      "X-Response-Time": "0.73879ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yqyJ424MEX37MNkWGugM8KgsSykbQWwqR3VDDlg7WO5vLIrgnznNmy%2BQct%2FQHFeQ3zEBOEylxUtQVWPB%2FgbHx%2BRjaWPz3J5FZghaUNcejHjuS2WNykYCLg%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475dbdc0c12acfb-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:94] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool for request_id: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:97] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "1c397645-ceac-45ff-b82e-27ea455d7466",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:116] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool name: ApiRequestNode for request_id: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:151] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing payload with component ApiRequestNode for request_id: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:94] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool for request_id: 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:97] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "22049bfe-d09e-483e-9e4c-940c1955dbeb",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:116] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool name: MergeDataComponent for request_id: 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:23 - ToolExecutor - INFO - [execute_tool:151] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing payload with component MergeDataComponent for request_id: 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:24 - ToolExecutor - INFO - [execute_tool:155] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Component MergeDataComponent processed payload successfully for request_id: 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:24 - ToolExecutor - INFO - [execute_tool:225] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor returning success: {
  "request_id": "22049bfe-d09e-483e-9e4c-940c1955dbeb",
  "status": "success",
  "result": {
    "status": "success",
    "result": {
      "value": "{\"new_value\":\"112\"}",
      "media": "help"
    }
  }
}
2025-05-29 17:35:24 - ToolExecutor - INFO - [execute_tool:155] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Component ApiRequestNode processed payload successfully for request_id: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ToolExecutor - INFO - [execute_tool:225] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "1c397645-ceac-45ff-b82e-27ea455d7466",
  "status": "success",
  "response": {
    "result": "1748520324242-9349877864588",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:05:24 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-K6kXbOVInJQCdEHhpU+cSie2pOE\"",
      "X-Response-Time": "0.66171ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=tlAo3TZFeX2w%2F5RaTeYl9Ur5xNSoGG2KERq14LOJGTKzeBRB4HBLNESBjjoxKxwlmICg6hAlci3Z9x4XajB2Zm2LUrN3vj2nG6aZdE2WKAQNycxwq082Pg%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475dc18cbf7e214-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool for request_id: a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "a908d36a-21dd-435b-9132-1eec76057a34",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:116] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool name: CombineTextComponent for request_id: a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:151] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Processing payload with component CombineTextComponent for request_id: a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:155] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Component CombineTextComponent processed payload successfully for request_id: a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:225] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor returning success: {
  "request_id": "a908d36a-21dd-435b-9132-1eec76057a34",
  "status": "success",
  "result": {
    "status": "success",
    "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
  }
}
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:94] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool for request_id: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:97] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "8089e511-3725-44b9-b6e6-3dab97eb428d",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:116] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool name: ApiRequestNode for request_id: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ToolExecutor - INFO - [execute_tool:151] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Processing payload with component ApiRequestNode for request_id: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ToolExecutor - INFO - [execute_tool:155] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Component ApiRequestNode processed payload successfully for request_id: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ToolExecutor - INFO - [execute_tool:225] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "8089e511-3725-44b9-b6e6-3dab97eb428d",
  "status": "success",
  "response": {
    "result": "1748520734540-8572188343387",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:12:14 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-C4A+S3DSkkf547mKeXMRuMNy3QU\"",
      "X-Response-Time": "0.84176ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=xAmH4N8fpj4cfM%2BmjcbBLoyrbocqecGpg6hSUHp6HN6zYtP09KCUOcnHj52fySkPyBm8ThgY2aq0f6qmBmaPU9ysEW0looFrtfXLFiZnPRWN20fzZreUSw%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475e61e7fb7e5bf-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:94] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool for request_id: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:97] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "f3248533-d896-4d3d-9aeb-f33a33ae02b8",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:116] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool name: ApiRequestNode for request_id: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:151] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Processing payload with component ApiRequestNode for request_id: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:94] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool for request_id: cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:97] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "cc2f3744-c28e-4966-a637-e6d614f8e6d3",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:116] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool name: MergeDataComponent for request_id: cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:151] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Processing payload with component MergeDataComponent for request_id: cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:155] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Component MergeDataComponent processed payload successfully for request_id: cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ToolExecutor - INFO - [execute_tool:186] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor returning error from component: {
  "request_id": "cc2f3744-c28e-4966-a637-e6d614f8e6d3",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:42:24 - ToolExecutor - INFO - [execute_tool:155] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Component ApiRequestNode processed payload successfully for request_id: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ToolExecutor - INFO - [execute_tool:225] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "f3248533-d896-4d3d-9aeb-f33a33ae02b8",
  "status": "success",
  "response": {
    "result": "1748520743903-9707870695274",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:12:23 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-2tLibyLkWDkYgCv6Yyl7o/5mx2Y\"",
      "X-Response-Time": "0.61744ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=o7AANdoipmwwV178j6%2Bfmb4xj8WXBZs9Xt5ujGr0MGCHaZ61hVzST0xCLKp99z4XJqq3NPbAKcwQZ0M7smvMPaiZ4loVf%2Bx%2BIv13HWpgE0qzRuuYA6YSdA%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475e658f804e1f8-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:94] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool for request_id: 612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:97] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "612859f2-5b34-4c50-b512-0a407f64eab3",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:116] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool name: CombineTextComponent for request_id: 612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:151] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing payload with component CombineTextComponent for request_id: 612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:155] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Component CombineTextComponent processed payload successfully for request_id: 612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:225] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor returning success: {
  "request_id": "612859f2-5b34-4c50-b512-0a407f64eab3",
  "status": "success",
  "result": {
    "status": "success",
    "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
  }
}
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:94] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool for request_id: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:97] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "4bc839a3-c1cf-4a86-a512-cc36eba4fc43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:116] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool name: ApiRequestNode for request_id: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:151] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing payload with component ApiRequestNode for request_id: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:155] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Component ApiRequestNode processed payload successfully for request_id: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ToolExecutor - INFO - [execute_tool:225] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "4bc839a3-c1cf-4a86-a512-cc36eba4fc43",
  "status": "success",
  "response": {
    "result": "1748520935375-5106869211886",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:15:35 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-O2F9PbPoiY+lVeYo+3xT5CbzmiQ\"",
      "X-Response-Time": "0.65551ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=l42CR%2BRM5kjZZXocERvPVR9CT061iHY5QMuvN4W63lWgHO4Nh6LQ2A9HDX8vKkvyzDlt7Odxw6lDQD6jUGo7%2FqnLLSDr5ToUFzjYYbQY7OzMEDaeuTc6Nw%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475eb045fbce1c1-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:94] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool for request_id: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:97] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "b23bd17d-ed71-479c-8f5e-53fa77244c29",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:116] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool name: ApiRequestNode for request_id: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:151] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing payload with component ApiRequestNode for request_id: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:94] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:97] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:116] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool name: MergeDataComponent for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:151] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing payload with component MergeDataComponent for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:155] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Component MergeDataComponent processed payload successfully for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:186] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor returning error from component: {
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:155] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Component ApiRequestNode processed payload successfully for request_id: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ToolExecutor - INFO - [execute_tool:225] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "b23bd17d-ed71-479c-8f5e-53fa77244c29",
  "status": "success",
  "response": {
    "result": "1748520944077-4253199549857",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:15:44 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-xIwp1mqwLq0OF+qf9vn8hJAA0CY\"",
      "X-Response-Time": "0.54968ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=F0OfYZ3Du1n1tfo1GZNUdprv0FzhkMbb5vb9pi92rtvU2xalDI7dQTb5IGSDh%2FybDUPIRpQxhqQYwZGsq%2Fv%2B0M%2FByjfd%2BTKVQG5T4q9SJHwxb7S7vs3VHQ%3D%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475eb3c0fb9e161-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
