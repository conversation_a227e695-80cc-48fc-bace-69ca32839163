{"nodes": [{"id": "TextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "TextComponent", "input_schema": {"predefined_fields": []}, "output_schema": {"predefined_fields": []}}]}, {"id": "OutputNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "OutputNode", "input_schema": {"predefined_fields": []}, "output_schema": {"predefined_fields": []}}]}], "transitions": [{"id": "transition-text-1", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "TextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "TextComponent", "tool_params": {"items": []}}], "input_data": [{"from_transition_id": "transition-start-1", "source_node_id": "start-1", "data_type": "string"}], "output_data": [{"to_transition_id": "transition-output-1", "target_node_id": "OutputNode", "data_type": "string"}, {"to_transition_id": "transition-output-2", "target_node_id": "OutputNode", "data_type": "string"}]}, "approval_required": false, "end": false, "conditional_routing": {"cases": [{"condition": {"source": "node_output", "operator": "equals", "expected_value": "success"}, "next_transition": "transition-output-1"}, {"condition": {"source": "node_output", "operator": "equals", "expected_value": ""}, "next_transition": "transition-output-2"}]}}, {"id": "transition-output-2", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "OutputNode", "tools_to_use": [{"tool_id": 1, "tool_name": "OutputNode", "tool_params": {"items": []}}], "input_data": [{"from_transition_id": "transition-text-1", "source_node_id": "TextComponent", "data_type": "string"}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-output-1", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "OutputNode", "tools_to_use": [{"tool_id": 1, "tool_name": "OutputNode", "tool_params": {"items": []}}], "input_data": [{"from_transition_id": "transition-text-1", "source_node_id": "TextComponent", "data_type": "string"}], "output_data": []}, "approval_required": false, "end": true}]}