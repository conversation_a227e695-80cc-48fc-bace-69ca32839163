2025-05-29 17:58:36 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-29\ApiRequestNode_17-58-36.log
2025-05-29 17:58:36 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-29 17:58:36 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:206] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Starting API request processing for request_id: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:224] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:232] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:234] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:240] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:260] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Final request body values - raw: {'technical': '12'}, json: None for request_id ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:267] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Request parameters extracted for request_id ed88ea01-bdeb-498d-ae87-c4470fd24846: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:345] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:361] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:391] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:392] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP BODY] None
2025-05-29 17:59:49 - ApiRequestNode - INFO - [process:393] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:409] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST COMPLETED] Duration: 0.884s, Status: 200, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:414] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846 
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:419] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:29:49 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-At2G18Fo1BORqGP69J6pfvEj7hY\"",
  "X-Response-Time": "0.72641ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=ihvBiP6jHrcxgiTIc6JAbRcFe0TuocUpxAWOjjI%2F7YJBRQ1BladlQwQR7hawk4%2FN0Go4CUDjEmRbzz9hru826caOdK%2BGjT5Sba52c1ApPkj8in9JowU%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475ffe018be1e71-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:430] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:588] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE BODY] Text: 1748521789747-3573702345602, RequestID: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ApiRequestNode - INFO - [process:598] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] API request successful: Status=200, RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:206] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Starting API request processing for request_id: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:224] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:232] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:234] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:240] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:260] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Final request body values - raw: {'technical': '12'}, json: None for request_id d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:267] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Request parameters extracted for request_id d8efe309-1471-4867-960e-a6d88e5c41bd: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:345] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:361] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:391] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:392] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP BODY] None
2025-05-29 17:59:57 - ApiRequestNode - INFO - [process:393] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:409] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP REQUEST COMPLETED] Duration: 0.695s, Status: 200, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:414] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd 
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:419] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:29:57 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-ih666MxzFe3W0bHng253rPSegVI\"",
  "X-Response-Time": "0.64883ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yQYAQRMzh4bVY%2FrAUKsRdvpTyFtVIFERm%2Buso03r5zlIOdapf7jlfB%2BpKpZ1Pc5dCsjUoohxHJ4S88Oqld0EkGeCw%2B64aev6hXEwMpvXjHoURf5ZIA0%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "947600111962e112-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:430] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:588] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] [HTTP RESPONSE BODY] Text: 1748521797587-9642807936761, RequestID: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ApiRequestNode - INFO - [process:598] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] API request successful: Status=200, RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd
