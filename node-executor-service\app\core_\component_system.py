"""
Component System - A fully dynamic and scalable component architecture.
"""

import asyncio
import importlib
import inspect
import json
import logging
import os
import pkgutil
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Set, Type, Optional, Callable, Tuple

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer
from aiokafka.structs import TopicPartition
from aiokafka.errors import (
    KafkaError,
    IllegalStateError,
    CommitFailedError,
    KafkaTimeoutError,
    NodeNotReadyError,
    RequestTimedOutError,
)

from app.config.config import settings

# Removed ValidationResult import
# from app.core_.base_component import ValidationResult

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger

    logger = setup_logger("ComponentSystem")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    logger = logging.getLogger(__name__)

# Global registry of components
COMPONENT_REGISTRY = {}


def register_component(component_type):
    """
    Decorator to register a component class.

    Args:
        component_type: The type of component (e.g., "api", "doc")

    Returns:
        The decorated class
    """

    def decorator(cls):
        logger.info(f"Registering component: {component_type} -> {cls.__name__}")
        COMPONENT_REGISTRY[component_type] = cls
        logger.info(
            f"Component registry now contains: {list(COMPONENT_REGISTRY.keys())}"
        )
        return cls

    return decorator


class ComponentManager:
    """
    Manages component discovery, instantiation, and execution.
    """

    def __init__(self, kafka_bootstrap_servers="localhost:9092"):
        """
        Initialize the component manager.

        Args:
            kafka_bootstrap_servers: Kafka bootstrap servers
        """
        # Initialize Kafka connection
        self.kafka_bootstrap_servers = kafka_bootstrap_servers
        logger.info(
            f"Initializing ComponentManager with Kafka bootstrap servers: {kafka_bootstrap_servers}"
        )

        # Log Kafka configuration from settings
        logger.info(f"Kafka Consumer Topic: {settings.kafka_consumer_topic}")
        logger.info(f"Kafka Results Topic: {settings.kafka_results_topic}")
        logger.info(f"Kafka Consumer Group ID: {settings.kafka_consumer_group_id}")
        logger.info(
            f"Kafka Producer Request Timeout: {settings.kafka_producer_request_timeout_ms}ms"
        )
        logger.info(
            f"Kafka Consumer Fetch Min Bytes: {settings.kafka_consumer_fetch_min_bytes}"
        )
        logger.info(
            f"Kafka Consumer Fetch Max Wait: {settings.kafka_consumer_fetch_max_wait_ms}ms"
        )
        logger.info(
            f"Kafka Consumer Session Timeout: {settings.kafka_consumer_session_timeout_ms}ms"
        )
        logger.info(
            f"Kafka Consumer Heartbeat Interval: {settings.kafka_consumer_heartbeat_interval_ms}ms"
        )
        logger.info(f"Default Node Retries: {settings.default_node_retries}")

        # Initialize component and task tracking
        self.components = {}
        self.producers = {}
        self.consumers = {}
        self.consumer_tasks = {}
        self.active_tasks = {}
        self.task_start_times = {}
        self.completed_tasks = {}
        self.failed_tasks = {}

        # Default concurrency settings - Now loaded from settings
        self.max_concurrent_tasks = settings.max_concurrent_tasks
        self.task_timeout = settings.component_task_timeout  # Load from settings
        self.worker_threads = settings.component_worker_threads  # Load from settings

        # Thread pool for CPU-bound operations
        logger.info(
            f"Initializing ThreadPoolExecutor with {self.worker_threads} workers"
        )
        self.thread_pool = ThreadPoolExecutor(max_workers=self.worker_threads)
        logger.info("ThreadPoolExecutor initialized")

        # Discover components
        self.discover_components()

    def discover_components(self):
        """
        Discover all registered components.
        """
        logger.info("Discovering components")
        logger.info(
            f"Component registry before discovery: {list(COMPONENT_REGISTRY.keys())}"
        )

        # Store components from registry
        for component_type, component_class in COMPONENT_REGISTRY.items():
            logger.info(
                f"Found component: {component_type} -> {component_class.__name__}"
            )
            self.components[component_type] = component_class()

        logger.info(f"Discovered components: {list(self.components.keys())}")

    def get_component_instance(self, component_type):
        """
        Get or create a component instance.

        Args:
            component_type: The type/name of the component

        Returns:
            An instance of the component

        Raises:
            ValueError: If the component type is not registered
        """
        # Return existing instance if available
        if component_type in self.components:
            return self.components[component_type]

        # Get the component class
        component_class = COMPONENT_REGISTRY.get(component_type)
        if not component_class:
            raise ValueError(f"Component type '{component_type}' is not registered")

        # Create a new instance
        instance = component_class()
        self.components[component_type] = instance
        return instance

    def get_topic_name(self, component_type, action):
        """
        Generate a topic name for a component and action.

        With the new implementation, we use a single topic for all components
        instead of component-specific topics.

        Args:
            component_type: The type/name of the component
            action: The action (e.g., "request", "response", "dlq")

        Returns:
            The topic name
        """
        # Use a single topic for all node-executor components
        if action == "request":
            topic = settings.kafka_consumer_topic
            logger.debug(f"Using request topic for {component_type}: {topic}")
            return topic
        elif action == "response":
            topic = settings.kafka_results_topic
            logger.debug(f"Using response topic for {component_type}: {topic}")
            return topic
        elif action == "dlq":
            # Keep DLQ topic for basic error handling, even if not strictly a "security" feature
            topic = f"{settings.kafka_consumer_topic}_dlq"
            logger.debug(f"Using DLQ topic for {component_type}: {topic}")
            return topic
        else:
            # Fallback to the old naming scheme for any other actions
            topic = f"{component_type}_{action}"
            logger.debug(f"Using fallback topic for {component_type}/{action}: {topic}")
            return topic

    async def get_producer(self, component_type):
        """
        Get or create a Kafka producer for a component.

        Args:
            component_type: The type/name of the component

        Returns:
            A Kafka producer
        """
        # Return existing producer if available
        if component_type in self.producers:
            logger.debug(
                f"Returning existing Kafka producer for component: {component_type}"
            )
            return self.producers[component_type]

        # Create a new producer - Load settings from config
        producer_kwargs = {
            "bootstrap_servers": self.kafka_bootstrap_servers,
            "value_serializer": lambda v: json.dumps(v).encode("utf-8"),
            "acks": "all",  # Ensure message is written to all in-sync replicas
            # "retries": settings.default_node_retries, # Removed - not supported by AIOKafkaProducer
            "request_timeout_ms": settings.kafka_producer_request_timeout_ms,  # Load from settings
            "enable_idempotence": True,  # Ensure exactly-once delivery semantics
        }

        # Log producer configuration details
        logger.info(
            f"Creating Kafka producer for component {component_type} with configuration:"
        )
        logger.info(f"  Bootstrap Servers: {self.kafka_bootstrap_servers}")
        logger.info(
            f"  Acks: all (ensuring message is written to all in-sync replicas)"
        )
        # Removed retries log as it's not supported by AIOKafkaProducer
        logger.info(
            f"  Request Timeout: {settings.kafka_producer_request_timeout_ms}ms"
        )
        logger.info(
            f"  Idempotence: Enabled (ensuring exactly-once delivery semantics)"
        )

        logger.info(
            f"Creating new Kafka producer for component: {component_type} with servers: {self.kafka_bootstrap_servers}"
        )
        producer = AIOKafkaProducer(**producer_kwargs)
        try:
            await producer.start()
            self.producers[component_type] = producer
            logger.info(
                f"Kafka producer started successfully for component: {component_type}"
            )
            return producer
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka producer timed out for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except KafkaError as e:
            logger.error(
                f"Failed to start Kafka producer for component {component_type}: {e}",
                exc_info=True,
            )
            raise

    async def start_component(self, component_type):
        """
        Start a component by creating a consumer for its request topic.

        With the new implementation, all components share a single topic.
        Each component will filter messages based on the target_component field.

        Args:
            component_type: The type/name of the component
        """
        # Get the component instance to ensure it exists and is initialized
        # This is important to do before setting up Kafka, even if we don't use the instance directly
        _ = self.get_component_instance(component_type)
        logger.debug(f"Verified component {component_type} exists and is initialized")

        # Get topic names - now using a single topic for all components
        request_topic = self.get_topic_name(component_type, "request")

        # Since we're using a single topic for all components, we don't need to check
        # if we already have a consumer for this topic. Each component will get its own consumer.
        # This simplifies the code and avoids issues with the Kafka consumer subscription.

        # Create a consumer - Load settings from config
        consumer_kwargs = {
            "bootstrap_servers": self.kafka_bootstrap_servers,
            "group_id": settings.kafka_consumer_group_id,  # Use setting for group ID
            "auto_offset_reset": "latest",  # Start consuming from the latest offset
            "enable_auto_commit": False,  # Manually commit offsets
            "fetch_min_bytes": settings.kafka_consumer_fetch_min_bytes,  # Load from settings
            "fetch_max_wait_ms": settings.kafka_consumer_fetch_max_wait_ms,  # Load from settings
            "session_timeout_ms": settings.kafka_consumer_session_timeout_ms,  # Load from settings
            "heartbeat_interval_ms": settings.kafka_consumer_heartbeat_interval_ms,  # Load from settings
        }

        # Log consumer configuration details
        logger.info(
            f"Creating Kafka consumer for component {component_type} with configuration:"
        )
        logger.info(f"  Bootstrap Servers: {self.kafka_bootstrap_servers}")
        logger.info(f"  Group ID: {settings.kafka_consumer_group_id}")
        logger.info(f"  Topic: {request_topic}")
        logger.info(f"  Auto Offset Reset: latest (starting from the latest offset)")
        logger.info(f"  Auto Commit: Disabled (using manual offset commits)")
        logger.info(f"  Fetch Min Bytes: {settings.kafka_consumer_fetch_min_bytes}")
        logger.info(f"  Fetch Max Wait: {settings.kafka_consumer_fetch_max_wait_ms}ms")
        logger.info(
            f"  Session Timeout: {settings.kafka_consumer_session_timeout_ms}ms"
        )
        logger.info(
            f"  Heartbeat Interval: {settings.kafka_consumer_heartbeat_interval_ms}ms"
        )

        logger.info(
            f"Creating new Kafka consumer for component: {component_type} on topic: {request_topic} with group_id: {settings.kafka_consumer_group_id}"
        )
        consumer = AIOKafkaConsumer(request_topic, **consumer_kwargs)

        # Start the consumer
        try:
            await consumer.start()
            self.consumers[component_type] = consumer
            logger.info(
                f"Kafka consumer started successfully for component: {component_type}"
            )

            # Initialize counters
            self.completed_tasks[component_type] = 0
            self.failed_tasks[component_type] = 0

            # Create a semaphore for concurrency control
            semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
            logger.debug(
                f"Created semaphore for component {component_type} with max_concurrent_tasks: {self.max_concurrent_tasks}"
            )

            # Start the consumer task
            consumer_task = asyncio.create_task(
                self._consume_messages(component_type, consumer, semaphore),
                name=f"{component_type}_consumer",
            )
            self.consumer_tasks[component_type] = consumer_task
            logger.debug(f"Created consumer task for component: {component_type}")

            logger.info(
                f"Started component: {component_type}, listening on topic: {request_topic}"
            )
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka consumer timed out for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out for component {component_type}: {e}",
                exc_info=True,
            )
            raise
        except KafkaError as e:
            logger.error(
                f"Failed to start Kafka consumer for component {component_type}: {e}",
                exc_info=True,
            )
            raise

    async def stop_component(self, component_type):
        """
        Stop a component by stopping its consumer.

        Args:
            component_type: The type/name of the component
        """
        logger.info(f"Stopping component: {component_type}")

        # Cancel the consumer task
        if component_type in self.consumer_tasks:
            logger.debug(f"Cancelling consumer task for component: {component_type}")
            self.consumer_tasks[component_type].cancel()
            try:
                await self.consumer_tasks[component_type]
                logger.debug(
                    f"Consumer task for component {component_type} cancelled successfully"
                )
            except asyncio.CancelledError:
                logger.debug(
                    f"Consumer task for component {component_type} confirmed cancelled"
                )
            except Exception as e:
                logger.error(
                    f"Error waiting for consumer task cancellation for component {component_type}: {e}",
                    exc_info=True,
                )
            del self.consumer_tasks[component_type]

        # Stop the consumer
        if component_type in self.consumers:
            logger.debug(f"Stopping Kafka consumer for component: {component_type}")
            try:
                await self.consumers[component_type].stop()
                logger.info(
                    f"Kafka consumer stopped successfully for component: {component_type}"
                )
            except KafkaError as e:
                logger.error(
                    f"Failed to stop Kafka consumer for component {component_type}: {e}",
                    exc_info=True,
                )
            del self.consumers[component_type]

        # Stop the producer
        if component_type in self.producers:
            logger.debug(f"Stopping Kafka producer for component: {component_type}")
            try:
                await self.producers[component_type].stop()
                logger.info(
                    f"Kafka producer stopped successfully for component: {component_type}"
                )
            except KafkaError as e:
                logger.error(
                    f"Failed to stop Kafka producer for component {component_type}: {e}",
                    exc_info=True,
                )
            del self.producers[component_type]

        logger.info(f"Stopped component: {component_type}")

    async def start_all_components(self):
        """Start all registered components."""
        logger.info("Starting all registered components...")
        for component_type in COMPONENT_REGISTRY:
            try:
                await self.start_component(component_type)
            except Exception as e:
                logger.error(
                    f"Failed to start component {component_type}: {e}", exc_info=True
                )
        logger.info("Finished attempting to start all components.")

    async def stop_all_components(self):
        """Stop all running components."""
        logger.info("Stopping all running components...")
        # Iterate over a copy of keys as the dictionary will be modified
        for component_type in list(self.consumer_tasks.keys()):
            try:
                await self.stop_component(component_type)
            except Exception as e:
                logger.error(
                    f"Failed to stop component {component_type}: {e}", exc_info=True
                )
        logger.info("Finished stopping all components.")

    async def _consume_messages(self, component_type, consumer, semaphore):
        """
        Consume messages for a component.

        Args:
            component_type: The type/name of the component
            consumer: The Kafka consumer
            semaphore: Semaphore for concurrency control
        """
        logger.info(f"Consumer loop started for component: {component_type}")
        try:
            # Get the component instance
            component = self.get_component_instance(component_type)
            logger.debug(
                f"Consumer loop got component instance: {component.__class__.__name__}"
            )

            # Process messages
            while True:
                try:
                    # Fetch messages with a timeout
                    fetched_messages = await consumer.getmany(
                        timeout_ms=1000, max_records=100
                    )
                    logger.debug(
                        f"Fetched {sum(len(msgs) for msgs in fetched_messages.values())} messages for component {component_type}"
                    )

                    if not fetched_messages:
                        # No messages, continue loop
                        await asyncio.sleep(0.1)  # Small sleep to prevent tight loop
                        continue

                    # Process fetched messages
                    for tp, messages in fetched_messages.items():
                        logger.debug(
                            f"Processing messages for TopicPartition {tp} for component {component_type}"
                        )
                        for msg in messages:
                            # Generate a task ID
                            task_id = f"{component_type}-{msg.topic}-{msg.partition}-{msg.offset}-{time.time()}"
                            logger.debug(
                                f"Received message: Topic={msg.topic}, Partition={msg.partition}, Offset={msg.offset}, TaskID={task_id}"
                            )

                            # Acquire semaphore
                            await semaphore.acquire()
                            logger.debug(
                                f"Semaphore acquired for TaskID={task_id}. Available: {semaphore._value}"
                            )

                            # Create a task to process the message
                            task = asyncio.create_task(
                                self._process_message(
                                    component_type, msg, semaphore, task_id
                                ),
                                name=f"{component_type}-{task_id}",
                            )

                            # Track the task
                            self.active_tasks[task_id] = task
                            self.task_start_times[task_id] = time.time()
                            logger.debug(f"Task created and tracked: TaskID={task_id}")

                            # Add done callback
                            task.add_done_callback(
                                lambda t, tid=task_id: self._task_done_callback(t, tid)
                            )

                except IllegalStateError:
                    # Consumer is not started or already closed, break loop
                    logger.warning(
                        f"Consumer for component {component_type} is in an illegal state. Exiting consume loop."
                    )
                    break
                except KafkaTimeoutError as e:
                    logger.error(
                        f"Kafka timeout during consumption for component {component_type}: {e}",
                        exc_info=True,
                    )
                    await asyncio.sleep(5)  # Wait before retrying consumption
                except NodeNotReadyError as e:
                    logger.error(
                        f"Kafka node not ready during consumption for component {component_type}: {e}",
                        exc_info=True,
                    )
                    await asyncio.sleep(
                        10
                    )  # Wait longer before retrying when node is not ready
                except RequestTimedOutError as e:
                    logger.error(
                        f"Kafka request timed out during consumption for component {component_type}: {e}",
                        exc_info=True,
                    )
                    await asyncio.sleep(5)  # Wait before retrying consumption
                except KafkaError as e:
                    logger.error(
                        f"Kafka error during consumption for component {component_type}: {e}",
                        exc_info=True,
                    )
                    await asyncio.sleep(5)  # Wait before retrying consumption
                except Exception as e:
                    logger.error(
                        f"Unexpected error during consumption for component {component_type}: {e}",
                        exc_info=True,
                    )
                    await asyncio.sleep(5)  # Wait before retrying consumption

        except asyncio.CancelledError:
            logger.info(f"Consumer task for component {component_type} cancelled")
        except Exception as e:
            logger.error(
                f"Error in consumer task for component {component_type}: {e}",
                exc_info=True,
            )
        finally:
            logger.info(f"Consumer loop finished for component: {component_type}")

    async def _process_message(self, component_type, msg, semaphore, task_id):
        """
        Process a message for a component.

        Args:
            component_type: The type/name of the component
            msg: The Kafka message
            semaphore: Semaphore for concurrency control
            task_id: The task ID
        """
        start_time = time.time()
        topic_partition = TopicPartition(msg.topic, msg.partition)
        offset = msg.offset

        # Detailed logging for message processing
        logger.info(
            f"Starting message processing: Component={component_type}, Topic={msg.topic}, Partition={msg.partition}, Offset={offset}, TaskID={task_id}"
        )
        logger.debug(f"Message details for TaskID={task_id}:")
        logger.debug(f"  Topic: {msg.topic}")
        logger.debug(f"  Partition: {msg.partition}")
        logger.debug(f"  Offset: {offset}")
        logger.debug(f"  Timestamp: {msg.timestamp}")
        logger.debug(f"  Timestamp Type: {msg.timestamp_type}")
        logger.debug(f"  Key: {msg.key}")
        logger.debug(f"  Headers: {msg.headers}")
        logger.debug(f"  Checksum: {msg.checksum}")
        logger.debug(f"  Serialized Key Size: {msg.serialized_key_size}")
        logger.debug(f"  Serialized Value Size: {msg.serialized_value_size}")

        payload = None  # Initialize payload to None
        try:
            # Get the component instance
            component = self.get_component_instance(component_type)
            logger.debug(
                f"Got component instance: {component.__class__.__name__}, TaskID={task_id}"
            )

            # Parse the message
            try:
                payload = json.loads(msg.value.decode("utf-8"))
                # Log the complete Kafka payload
                logger.info(
                    f"Received Kafka message: TaskID={task_id}, Payload={json.dumps(payload, indent=2)}"
                )
                logger.debug(f"Successfully parsed message payload: TaskID={task_id}")
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse message as JSON: {e}, TaskID={task_id}")
                logger.debug(f"Raw message value: {msg.value}")
                # Send error response for unparseable payload
                await self._send_error(
                    component_type,
                    {"request_id": "unparseable"},
                    f"Failed to parse message as JSON: {str(e)}",
                )
                self.failed_tasks[component_type] += 1
                # Commit offset even on JSON error to avoid reprocessing bad messages
                await self._commit_offset(
                    component_type, topic_partition, offset + 1, task_id
                )
                return  # Skip processing if JSON is invalid

            # Get the request ID and correlation ID from the payload
            request_id = payload.get("request_id", "unknown")
            correlation_id = payload.get("correlation_id")

            # Import RequestContext to set the correlation_id in the context
            from app.utils.logging_config import RequestContext

            # Set the request_id in the context
            RequestContext.set_request_id(request_id)

            # Set the correlation_id in the context if it exists
            if correlation_id:
                RequestContext.set_correlation_id(correlation_id)
                logger.debug(
                    f"Request ID from payload: {request_id}, Correlation ID: {correlation_id}, TaskID={task_id}"
                )
            else:
                logger.debug(
                    f"Request ID from payload: {request_id}, No Correlation ID found, TaskID={task_id}"
                )

            # Check if the message contains a tool_name field
            # If it does, we'll use the ToolExecutor to handle it
            tool_name = payload.get("tool_name")
            if tool_name:
                logger.debug(
                    f"Message contains tool_name: {tool_name}, will be handled by ToolExecutor. "
                    f"TaskID={task_id}, RequestID={request_id}"
                )

                # Import here to avoid circular imports
                from app.core_.tool_executor import get_tool_executor

                try:
                    # Get the tool executor
                    tool_executor = get_tool_executor()
                    logger.debug(
                        f"Got ToolExecutor instance for tool_name: {tool_name}, TaskID={task_id}"
                    )

                    # Execute the tool
                    logger.info(
                        f"Executing tool {tool_name} for RequestID={request_id}, TaskID={task_id}"
                    )
                    result = await tool_executor.execute_tool(payload)
                    logger.info(
                        f"Tool {tool_name} executed successfully for RequestID={request_id}, TaskID={task_id}"
                    )

                    # Send the result
                    await self._send_result(component_type, payload, result)

                    # Update success counter
                    self.completed_tasks[component_type] += 1

                    # Commit offset after successful processing
                    await self._commit_offset(
                        component_type, topic_partition, offset + 1, task_id
                    )
                    return
                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {str(e)}"
                    logger.error(
                        f"{error_msg}: TaskID={task_id}, RequestID={request_id}",
                        exc_info=True,
                    )

                    # Send error response
                    await self._send_error(component_type, payload, error_msg)

                    # Update failure counter
                    self.failed_tasks[component_type] += 1

                    # Commit offset on error
                    await self._commit_offset(
                        component_type, topic_partition, offset + 1, task_id
                    )
                    return

            # For backward compatibility, check if the message contains a target_component field
            # If it does, and it doesn't match our component_type, skip processing
            target_component = payload.get("target_component")
            if target_component and target_component != component_type:
                logger.debug(
                    f"Skipping message for component {target_component}, "
                    f"received by {component_type}: TaskID={task_id}, RequestID={request_id}"
                )
                # Commit offset for skipped messages
                await self._commit_offset(
                    component_type, topic_partition, offset + 1, task_id
                )
                return  # Skip processing

            # Check if the message contains a consumer_group field
            # This is used for testing to ensure the message is processed by the right component
            consumer_group = payload.get("consumer_group")
            if consumer_group:
                logger.info(
                    f"Message has consumer_group: {consumer_group}, component: {component_type}, TaskID={task_id}, RequestID={request_id}"
                )

            logger.info(
                f"Processing message for component {component_type}: TaskID={task_id}, RequestID={request_id}"
            )
            logger.debug(f"Full payload for TaskID={task_id}: {payload}")

            # Process the message
            try:
                # Validate the payload using the component's validate method if available
                if hasattr(component, "validate"):
                    logger.debug(
                        f"Running validation for component {component_type}, TaskID={task_id}"
                    )
                    validation_result = await component.validate(payload)

                    # Handle both boolean and ValidationResult returns
                    is_valid = getattr(
                        validation_result, "is_valid", validation_result
                    )  # Handles both bool and ValidationResult
                    error_msg = getattr(
                        validation_result, "error_message", "Payload validation failed"
                    )  # Handles ValidationResult
                    error_details = getattr(
                        validation_result, "error_details", {}
                    )  # Handles ValidationResult

                    if not is_valid:
                        logger.error(
                            f"Validation failed for TaskID={task_id}: {error_msg}"
                        )
                        await self._send_error(
                            component_type, payload, error_msg, error_details
                        )
                        self.failed_tasks[component_type] += 1
                        # Commit offset on validation failure
                        await self._commit_offset(
                            component_type, topic_partition, offset + 1, task_id
                        )
                        return  # Stop processing on validation failure
                    logger.debug(f"Validation successful for TaskID={task_id}")

                # Create a task for execution
                logger.debug(
                    f"Creating execution task for component {component_type}, TaskID={task_id}"
                )
                execution_task = asyncio.create_task(component.process(payload))

                # Wait for execution with timeout
                logger.debug(
                    f"Waiting for execution task with timeout {self.task_timeout}s, TaskID={task_id}"
                )
                result = await asyncio.wait_for(
                    execution_task, timeout=self.task_timeout
                )
                logger.debug(f"Execution task completed for TaskID={task_id}")

                # Log success
                execution_time = time.time() - start_time
                logger.info(
                    f"Successfully processed message for component {component_type}: "
                    f"TaskID={task_id}, RequestID={request_id}, ExecutionTime={execution_time:.2f}s"
                )

                # Send the result
                await self._send_result(component_type, payload, result)

                # Update success counter
                self.completed_tasks[component_type] += 1

                # Commit offset after successful processing
                await self._commit_offset(
                    component_type, topic_partition, offset + 1, task_id
                )

            except asyncio.TimeoutError:
                # Handle timeout
                if not execution_task.done():
                    execution_task.cancel()
                    logger.warning(
                        f"Execution task cancelled due to timeout for TaskID={task_id}"
                    )

                error_msg = f"Execution timed out after {self.task_timeout} seconds"
                logger.error(
                    f"{error_msg}: Component={component_type}, TaskID={task_id}, RequestID={request_id}"
                )

                # Send error response
                await self._send_error(component_type, payload, error_msg)

                # Update failure counter
                self.failed_tasks[component_type] += 1
                # Commit offset on timeout
                await self._commit_offset(
                    component_type, topic_partition, offset + 1, task_id
                )

            except Exception as e:
                error_msg = f"Error processing message: {e}"
                logger.error(
                    f"{error_msg}: Component={component_type}, TaskID={task_id}, RequestID={request_id}",
                    exc_info=True,
                )

                # Send error response if possible
                try:
                    # Ensure payload is available before sending error
                    if payload is not None:
                        await self._send_error(component_type, payload, str(e))
                    else:
                        # If payload parsing failed, send a generic error without original payload
                        await self._send_error(
                            component_type,
                            {"request_id": request_id},
                            f"Error processing message (payload unparseable): {str(e)}",
                        )
                except Exception as send_error_e:
                    logger.error(
                        f"Failed to send error response after processing error for TaskID={task_id}: {send_error_e}",
                        exc_info=True,
                    )

                self.failed_tasks[component_type] += 1
                # Commit offset on processing error
                await self._commit_offset(
                    component_type, topic_partition, offset + 1, task_id
                )

        except Exception as e:
            # This catch block handles errors *before* the inner try/except,
            # such as errors getting the component instance or initial payload parsing.
            error_msg = f"Critical error before processing message: {e}"
            logger.critical(
                f"{error_msg}: Component={component_type}, TaskID={task_id}",
                exc_info=True,
            )

            # Attempt to send a generic error response if payload wasn't parsed
            if payload is None:
                try:
                    await self._send_error(
                        component_type,
                        {"request_id": "unparseable"},
                        f"Critical error processing message: {str(e)}",
                    )
                except Exception as send_error_e:
                    logger.error(
                        f"Failed to send critical error response for TaskID={task_id}: {send_error_e}",
                        exc_info=True,
                    )
            # If payload was parsed, the inner block would have handled sending an error

            self.failed_tasks[component_type] += 1
            # Commit offset on critical error
            await self._commit_offset(
                component_type, topic_partition, offset + 1, task_id
            )

        finally:
            # Release the semaphore
            semaphore.release()
            logger.debug(
                f"Semaphore released for TaskID={task_id}. Available: {semaphore._value}"
            )
            logger.info(
                f"Finished message processing: Component={component_type}, Topic={msg.topic}, Partition={msg.partition}, Offset={offset}, TaskID={task_id}"
            )

    async def _commit_offset(self, component_type, topic_partition, offset, task_id):
        """
        Commit the consumer offset.

        Args:
            component_type: The type/name of the component
            topic_partition: The TopicPartition object
            offset: The offset to commit
            task_id: The task ID
        """
        if component_type not in self.consumers:
            logger.warning(
                f"Attempted to commit offset for component {component_type} but consumer is not found. TaskID={task_id}"
            )
            return

        try:
            logger.debug(
                f"Attempting to commit offset {offset} for {topic_partition} for component {component_type}, TaskID={task_id}"
            )
            await self.consumers[component_type].commit({topic_partition: offset})
            logger.info(
                f"Successfully committed offset {offset} for {topic_partition} for component {component_type}, TaskID={task_id}"
            )
        except CommitFailedError as e:
            logger.error(
                f"Commit failed for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka timeout during commit for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready during commit for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out during commit for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )
        except KafkaError as e:
            logger.error(
                f"Kafka error during commit for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"Unexpected error during commit for {topic_partition} at offset {offset} for component {component_type}, TaskID={task_id}: {e}",
                exc_info=True,
            )

    async def _send_result(self, component_type, request, result):
        """
        Send a result for a component.

        Args:
            component_type: The type/name of the component
            request: The original request
            result: The result to send
        """
        request_id = request.get("request_id", "unknown")
        logger.info(
            f"Preparing to send result for component {component_type}, RequestID={request_id}"
        )

        try:
            # Get the producer
            producer = await self.get_producer(component_type)
            logger.debug(
                f"Got producer for component {component_type} to send result for RequestID={request_id}"
            )

            # Get the response topic
            response_topic = self.get_topic_name(component_type, "response")
            logger.debug(
                f"Using response topic {response_topic} for component {component_type}, RequestID={request_id}"
            )

            # Determine the result status and extract actual result data
            result_status = "success"
            actual_result = result
            error_message = None

            if isinstance(result, dict):
                # Check for error status
                if result.get("status") == "error":
                    result_status = "error"
                    # Extract error message from various possible locations
                    if "error" in result:
                        error_message = result["error"]
                    elif "result" in result and isinstance(result["result"], dict) and "error" in result["result"]:
                        error_message = result["result"]["error"]
                    else:
                        error_message = "Unknown error occurred"
                    logger.info(f"Result has error status for RequestID={request_id}: {error_message}")

                # Check for direct error field
                elif "error" in result and result["error"] is not None:
                    result_status = "error"
                    error_message = result["error"]
                    logger.info(f"Result contains error field for RequestID={request_id}: {error_message}")

                else:
                    # For success responses, clean up the result
                    cleaned_result = result.copy()
                    # Remove status field to avoid duplication
                    if "status" in cleaned_result:
                        del cleaned_result["status"]
                    # Remove null error fields
                    if "error" in cleaned_result and cleaned_result["error"] is None:
                        del cleaned_result["error"]
                    actual_result = cleaned_result

            # Create standardized response structure
            response = {
                "request_id": request_id,
                "component_type": component_type,
                "status": result_status,
                "timestamp": time.time(),
            }

            # Add result or error based on status
            if result_status == "error":
                response["result"] = None
                response["error"] = error_message
            else:
                response["result"] = actual_result
                response["error"] = None

            logger.debug(
                f"Created response message with status={result_status} for RequestID={request_id}"
            )

            # Log the complete response
            logger.info(
                f"Sending Kafka response: RequestID={request_id}, Response={json.dumps(response, indent=2)}"
            )

            # Send the response
            logger.debug(
                f"Sending result message to topic {response_topic} for RequestID={request_id}"
            )
            await producer.send_and_wait(response_topic, response)
            logger.info(
                f"Sent result for component {component_type} to topic {response_topic} for RequestID={request_id}"
            )
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka timeout sending result to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready when sending result to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out when sending result to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except KafkaError as e:
            logger.error(
                f"Failed to send result to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"Unexpected error sending result for RequestID={request_id}: {e}",
                exc_info=True,
            )

    async def _send_error(
        self, component_type, request, error_message, error_details=None
    ):
        """
        Send an error for a component.

        Args:
            component_type: The type/name of the component
            request: The original request
            error_message: The error message
            error_details: Detailed error information (optional, not used in response due to security considerations)
        """
        # Note: error_details parameter is kept for backward compatibility but not used in the response
        # to avoid potentially exposing sensitive information
        request_id = request.get("request_id", "unknown")
        logger.info(
            f"Preparing to send error for component {component_type}, RequestID={request_id}"
        )

        try:
            # Get the producer
            producer = await self.get_producer(component_type)
            logger.debug(
                f"Got producer for component {component_type} to send error for RequestID={request_id}"
            )

            # Get the response topic
            response_topic = self.get_topic_name(component_type, "response")
            logger.debug(
                f"Using response topic {response_topic} for component {component_type}, RequestID={request_id}"
            )

            # Create standardized error response
            response = {
                "request_id": request_id,
                "component_type": component_type,
                "status": "error",
                "timestamp": time.time(),
                "result": None,
                "error": error_message,
            }
            logger.debug(f"Created error response message for RequestID={request_id}")

            # Log the complete error response
            logger.info(
                f"Sending Kafka error response: RequestID={request_id}, Response={json.dumps(response, indent=2)}"
            )

            # Send the error response
            logger.debug(
                f"Sending error message to topic {response_topic} for RequestID={request_id}"
            )
            await producer.send_and_wait(response_topic, response)
            logger.info(
                f"Sent error for component {component_type} to topic {response_topic} for RequestID={request_id}"
            )
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka timeout sending error response to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready when sending error response to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out when sending error response to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except KafkaError as e:
            logger.error(
                f"Failed to send error response to topic {response_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"Unexpected error sending error response for RequestID={request_id}: {e}",
                exc_info=True,
            )

        # Also send to DLQ (keeping DLQ for basic error handling)
        dlq_topic = self.get_topic_name(component_type, "dlq")
        logger.debug(
            f"Preparing to send message to DLQ topic {dlq_topic} for RequestID={request_id}"
        )
        dlq_message = {
            "original_request": request,
            "component_type": component_type,  # Include the component type in the DLQ message
            "error": error_message,
            "timestamp": time.time(),
        }
        try:
            logger.debug(
                f"Sending message to DLQ topic {dlq_topic} for RequestID={request_id}"
            )
            await producer.send_and_wait(dlq_topic, dlq_message)
            logger.info(
                f"Sent message to DLQ for component {component_type} to topic {dlq_topic} for RequestID={request_id}"
            )
        except KafkaTimeoutError as e:
            logger.error(
                f"Kafka timeout sending message to DLQ topic {dlq_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except NodeNotReadyError as e:
            logger.error(
                f"Kafka node not ready when sending message to DLQ topic {dlq_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except RequestTimedOutError as e:
            logger.error(
                f"Kafka request timed out when sending message to DLQ topic {dlq_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except KafkaError as e:
            logger.error(
                f"Failed to send message to DLQ topic {dlq_topic} for RequestID={request_id}: {e}",
                exc_info=True,
            )
        except Exception as e:
            logger.error(
                f"Unexpected error sending DLQ message for RequestID={request_id}: {e}",
                exc_info=True,
            )

    async def send_error_response(
        self,
        reply_topic: str,
        error_message: str,
        request_id: str,
        component_type: str = None,
    ) -> None:
        """
        Send an error response to a specific topic.

        Args:
            reply_topic: The topic to send the error response to
            error_message: The error message
            request_id: The request ID
            component_type: The component type (optional, uses the first available producer if not specified)
        """
        message = {
            "request_id": request_id,
            "component_type": component_type or "generic",
            "status": "error",
            "timestamp": time.time(),
            "result": None,
            "error": error_message,
        }

        if reply_topic:
            # Get a producer - either for the specified component or the first available
            if component_type and component_type in self.producers:
                producer = self.producers[component_type]
            elif self.producers:
                # Use the first available producer if component_type not specified
                component_type = next(iter(self.producers))
                producer = self.producers[component_type]
            else:
                # No producers available, create one for a generic component
                producer = await self.get_producer("generic")

            # Send the message
            try:
                await producer.send_and_wait(reply_topic, message)
                logger.info(f"Sent error response to topic '{reply_topic}': {message}")
            except Exception as e:
                logger.error(
                    f"Failed to send error response to topic '{reply_topic}': {e}",
                    exc_info=True,
                )

    def _task_done_callback(self, task, task_id):
        """
        Callback when a task is done.

        Args:
            task: The completed task
            task_id: The task ID
        """
        logger.debug(f"Task done callback triggered for TaskID={task_id}")
        # Remove from tracking
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
            logger.debug(f"Removed TaskID={task_id} from active_tasks")
        if task_id in self.task_start_times:
            del self.task_start_times[task_id]
            logger.debug(f"Removed TaskID={task_id} from task_start_times")

        # Check for exceptions
        if task.exception():
            logger.error(
                f"Task {task_id} failed with exception: {task.exception()}",
                exc_info=True,
            )
        else:
            logger.debug(f"Task {task_id} completed without exception")


# Function to discover and import all components
def discover_component_modules():
    """
    Discover and import all component modules in the app/components directory.
    This eliminates the need to manually import each component in main.py.

    Returns:
        A list of imported component module names
    """
    logger.info("Discovering component modules")
    components_dir = os.path.join("app", "components")
    imported_modules = []

    # Check if the components directory exists
    if not os.path.exists(components_dir) or not os.path.isdir(components_dir):
        logger.warning(f"Components directory not found: {components_dir}")
        return imported_modules

    # Get all Python files in the components directory
    component_files = [
        f
        for f in os.listdir(components_dir)
        if f.endswith(".py") and not f.startswith("__")
    ]

    logger.info(
        f"Found {len(component_files)} potential component files: {component_files}"
    )

    # Import each component file
    for file_name in component_files:
        module_name = file_name[:-3]  # Remove .py extension
        full_module_name = f"app.components.{module_name}"

        try:
            logger.info(f"Importing component module: {full_module_name}")
            logger.debug(f"Registry before import: {list(COMPONENT_REGISTRY.keys())}")
            importlib.import_module(full_module_name)
            imported_modules.append(full_module_name)
            logger.info(f"Successfully imported component module: {full_module_name}")
            logger.debug(f"Registry after import: {list(COMPONENT_REGISTRY.keys())}")
        except Exception as e:
            logger.error(
                f"Error importing component module {full_module_name}: {str(e)}"
            )
            logger.debug(f"Import exception details: {traceback.format_exc()}")
            # Continue with other modules even if one fails

    logger.info(
        f"Imported {len(imported_modules)} component modules: {imported_modules}"
    )
    return imported_modules


# Create a global component manager
component_manager = None


def get_component_manager(kafka_bootstrap_servers="localhost:9092"):
    """
    Get or create the global component manager.

    Args:
        kafka_bootstrap_servers: Kafka bootstrap servers

    Returns:
        The component manager
    """
    global component_manager
    if component_manager is None:
        logger.info("Creating new global ComponentManager instance")
        component_manager = ComponentManager(kafka_bootstrap_servers)
    logger.debug("Returning global ComponentManager instance")
    return component_manager
