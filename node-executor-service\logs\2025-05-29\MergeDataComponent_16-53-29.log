2025-05-29 16:53:29 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-29\MergeDataComponent_16-53-29.log
2025-05-29 17:01:55 - MergeDataComponent - INFO - [__init__:96] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] MergeDataExecutor initialized
2025-05-29 17:01:55 - MergeDataComponent - INFO - [process:241] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Processing merge data request for request_id: a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - MergeDataComponent - INFO - [process:243] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:01:55 - MergeDataComponent - INFO - [process:257] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:01:55 - MergeDataComponent - INFO - [process:277] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Merging data for request_id a75efa9f-6545-4261-ac84-aba74622d7ea. Strategy: 'None', Num additional inputs: 1
2025-05-29 17:01:55 - MergeDataComponent - ERROR - [process:365] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Unknown merge strategy for request_id a75efa9f-6545-4261-ac84-aba74622d7ea: None
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:241] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing merge data request for request_id: 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:243] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:257] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:277] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Merging data for request_id 20dedc4d-6b19-4a22-86fe-a73e762633c9. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:341] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Dictionary input_1 merged with deep merge strategy for request_id 20dedc4d-6b19-4a22-86fe-a73e762633c9. Current keys: ['value', 'social']
2025-05-29 17:04:08 - MergeDataComponent - INFO - [process:380] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] All data merged successfully for request_id 20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:241] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Processing merge data request for request_id: 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:243] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:257] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:277] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Merging data for request_id 53adf93a-c760-48fc-a088-529088348acd. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:341] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Dictionary input_1 merged with deep merge strategy for request_id 53adf93a-c760-48fc-a088-529088348acd. Current keys: ['value', 'media']
2025-05-29 17:04:18 - MergeDataComponent - INFO - [process:380] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] All data merged successfully for request_id 53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:241] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing merge data request for request_id: 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:243] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:257] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:277] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Merging data for request_id 6f1f3354-4496-401b-9842-4fcdc004df99. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:341] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Dictionary input_1 merged with deep merge strategy for request_id 6f1f3354-4496-401b-9842-4fcdc004df99. Current keys: ['value', 'social']
2025-05-29 17:10:16 - MergeDataComponent - INFO - [process:380] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] All data merged successfully for request_id 6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:241] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Processing merge data request for request_id: 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:243] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:257] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:277] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Merging data for request_id 4fbc78b5-9b1b-4357-ab42-407864302c0d. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:341] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Dictionary input_1 merged with deep merge strategy for request_id 4fbc78b5-9b1b-4357-ab42-407864302c0d. Current keys: ['value', 'media']
2025-05-29 17:10:24 - MergeDataComponent - INFO - [process:380] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] All data merged successfully for request_id 4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:241] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Processing merge data request for request_id: be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:243] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:257] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:277] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Merging data for request_id be6ecb6e-1c4a-4d66-9017-5852c2efc0d3. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:341] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Dictionary input_1 merged with deep merge strategy for request_id be6ecb6e-1c4a-4d66-9017-5852c2efc0d3. Current keys: ['value', 'social']
2025-05-29 17:31:58 - MergeDataComponent - INFO - [process:380] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] All data merged successfully for request_id be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:241] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing merge data request for request_id: c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:243] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:257] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:277] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Merging data for request_id c3220f92-eb78-49a0-afca-1353ca12850b. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:341] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Dictionary input_1 merged with deep merge strategy for request_id c3220f92-eb78-49a0-afca-1353ca12850b. Current keys: ['value', 'social']
2025-05-29 17:35:14 - MergeDataComponent - INFO - [process:380] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] All data merged successfully for request_id c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:23 - MergeDataComponent - INFO - [process:241] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Processing merge data request for request_id: 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:23 - MergeDataComponent - INFO - [process:243] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:35:23 - MergeDataComponent - INFO - [process:257] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:35:23 - MergeDataComponent - INFO - [process:277] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Merging data for request_id 22049bfe-d09e-483e-9e4c-940c1955dbeb. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:35:24 - MergeDataComponent - INFO - [process:341] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Dictionary input_1 merged with deep merge strategy for request_id 22049bfe-d09e-483e-9e4c-940c1955dbeb. Current keys: ['value', 'media']
2025-05-29 17:35:24 - MergeDataComponent - INFO - [process:380] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] All data merged successfully for request_id 22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:42:23 - MergeDataComponent - INFO - [process:241] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Processing merge data request for request_id: cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - MergeDataComponent - INFO - [process:243] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:42:23 - MergeDataComponent - INFO - [process:257] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:42:23 - MergeDataComponent - INFO - [process:277] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Merging data for request_id cc2f3744-c28e-4966-a637-e6d614f8e6d3. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:42:23 - MergeDataComponent - ERROR - [process:308] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 17:45:44 - MergeDataComponent - INFO - [process:241] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing merge data request for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - MergeDataComponent - INFO - [process:243] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:45:44 - MergeDataComponent - INFO - [process:257] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:45:44 - MergeDataComponent - INFO - [process:277] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Merging data for request_id 7a321b15-3271-4842-bad7-8266c74a7d43. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:45:44 - MergeDataComponent - ERROR - [process:308] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
