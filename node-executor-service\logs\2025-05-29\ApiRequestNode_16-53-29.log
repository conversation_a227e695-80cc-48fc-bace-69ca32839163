2025-05-29 16:53:29 - ApiRequestNode - INFO - [setup_logger:467] Logger ApiRequestNode configured with log file: logs\2025-05-29\ApiRequestNode_16-53-29.log
2025-05-29 16:53:29 - ApiRequestNode - INFO - [__init__:64] Initializing API Component
2025-05-29 16:53:29 - ApiRequestNode - INFO - [__init__:67] API Component initialized successfully
2025-05-29 17:01:54 - ApiRequestNode - INFO - [process:206] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Starting API request processing for request_id: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:224] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:232] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:234] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:240] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:260] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Final request body values - raw: {'technical': '12'}, json: None for request_id 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:267] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Request parameters extracted for request_id 4156040d-38a6-49db-b202-d383397630ea: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:345] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:361] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:391] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:392] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP BODY] None
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:393] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:409] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP REQUEST COMPLETED] Duration: 0.918s, Status: 200, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:414] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: 4156040d-38a6-49db-b202-d383397630ea 
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:419] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 11:31:55 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-Coxo3/byphWYxA+8pyAdVjMvcpc\"",
  "X-Response-Time": "0.51569ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=kgIGji3HQ3OUxQwMHeLB1jVyShxCQC8pBlXpCBek5gPKJkCeM36QwU5%2BxlcRy0z5uP2V3mxIXbdGbbx1FPfA0Kkgq9lpb%2B%2FT96XB2cbAxYxjq2Kdi1o%2FEA%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475ab0e6b4fe161-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:430] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:588] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] [HTTP RESPONSE BODY] Text: 1748518315543-9699916734825, RequestID: 4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ApiRequestNode - INFO - [process:598] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] API request successful: Status=200, RequestID=4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:206] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Starting API request processing for request_id: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:224] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:232] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:234] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:240] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:260] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Final request body values - raw: {'technical': '12'}, json: None for request_id eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:267] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Request parameters extracted for request_id eb7c4840-2e2c-45ff-8b32-5f289a5167ce: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:345] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:361] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:391] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:392] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP BODY] None
2025-05-29 17:04:08 - ApiRequestNode - INFO - [process:393] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:409] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST COMPLETED] Duration: 0.680s, Status: 200, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:414] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce 
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:419] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 11:34:09 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-Wg55KgkBTPfolaGIcnCfH2ztqnA\"",
  "X-Response-Time": "0.63825ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=uBNcG%2FLovATJkOsVTYy2OuEbAwUh%2BGQoO22F728W9XzbH8B3FWZrUVGEwmnvKOpRP%2BrHQ5GjS%2F7nsK5JWz6jrygUEUR7F3PrfsAD6eC71PoM1rRBiP20VQ%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475ae519b08e1fd-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:430] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:588] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE BODY] Text: 1748518449180-7919062976725, RequestID: eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ApiRequestNode - INFO - [process:598] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] API request successful: Status=200, RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:206] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Starting API request processing for request_id: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:224] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:232] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:234] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:240] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:260] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Final request body values - raw: {'technical': '12'}, json: None for request_id ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:267] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Request parameters extracted for request_id ec2122d4-c3b0-4386-a592-683439438d95: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:345] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:361] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:391] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:392] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP BODY] None
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:393] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:409] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP REQUEST COMPLETED] Duration: 0.667s, Status: 200, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:414] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: ec2122d4-c3b0-4386-a592-683439438d95 
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:419] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 11:34:18 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-l8lKaC21Vesm5oba7Fk4dcWQqZs\"",
  "X-Response-Time": "0.49125ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=80qxtokT5vxAS3oOT7hk4m6n%2FDB4pKcIZ5RCWXkPBXPi1FLqnnoafjI1TqcuW8UaJKRfq%2F%2FzmE3V9nvcTb%2BmydabYu80Zm1c8SAW6%2BnLMi85fw11pgYOXQ%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475ae8bce3ce17b-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:430] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:588] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] [HTTP RESPONSE BODY] Text: 1748518458479-5113869060296, RequestID: ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ApiRequestNode - INFO - [process:598] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] API request successful: Status=200, RequestID=ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:206] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Starting API request processing for request_id: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:224] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:232] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:234] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:240] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:260] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Final request body values - raw: {'technical': '12'}, json: None for request_id 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:267] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Request parameters extracted for request_id 315f43ec-5f25-47ca-a36e-9d853e41b475: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:345] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:361] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:391] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:392] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP BODY] None
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:393] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:409] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST COMPLETED] Duration: 0.822s, Status: 200, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:414] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475 
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:419] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 11:40:16 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-9+pwHn8MwsTiIIwmpJHaq4Qqnu8\"",
  "X-Response-Time": "0.62231ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=bIAJGO5z6IQy6aaR9Bt%2B3QZNJ%2BDi0ayJ9N%2BeYMElG5IDxKWccdOE11qbhw8yonwrLJesUofa5sJnXYSWxLuaIxx8%2FXkPfK0Eq9fclQ5bFMox4rfqYONTtg%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475b748f9f7e1cc-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:430] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:588] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE BODY] Text: 1748518816428-6256029747892, RequestID: 315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ApiRequestNode - INFO - [process:598] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] API request successful: Status=200, RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:206] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Starting API request processing for request_id: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:224] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:232] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:234] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:240] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:260] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Final request body values - raw: {'technical': '12'}, json: None for request_id bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:267] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Request parameters extracted for request_id bdcfd532-d649-4cde-a293-019db805555a: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:345] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:361] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:391] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:392] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP BODY] None
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:393] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:409] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP REQUEST COMPLETED] Duration: 0.505s, Status: 200, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:414] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: bdcfd532-d649-4cde-a293-019db805555a 
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:419] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 11:40:24 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-bIGiOAHo1yGDshmmJ0AJQyzz4Y8\"",
  "X-Response-Time": "0.57367ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=mcdgNi0rHEG%2F2JUjGkLnmVOFFbU9W0Srwmp9eKayQRDH0WU3l%2FX91hvIWSudPjrqVyg7CBvQw2T3Ns5oIK4LLifFZlvtB%2FvMJ7KKOQmk9FtAPPxBo%2BSPVA%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475b77a998be1cd-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:430] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:588] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] [HTTP RESPONSE BODY] Text: 1748518824170-5158934467472, RequestID: bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ApiRequestNode - INFO - [process:598] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] API request successful: Status=200, RequestID=bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:206] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Starting API request processing for request_id: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:224] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Extracting request new parameters https://www.postb.in/1748518229727-4452951683197, POST, {}, {} for request_id 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:232] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:234] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:240] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:260] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Final request body values - raw: {'technical': '12'}, json: None for request_id 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:267] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Request parameters extracted for request_id 15c42e55-1b6b-4004-8650-5222caa8cabe: URL=https://www.postb.in/1748518229727-4452951683197, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:345] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748518229727-4452951683197, Timeout: Nones, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:361] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:391] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748518229727-4452951683197 RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:392] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP BODY] None
2025-05-29 17:31:58 - ApiRequestNode - INFO - [process:393] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:31:59 - ApiRequestNode - INFO - [process:409] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP REQUEST COMPLETED] Duration: 0.923s, Status: 404, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ApiRequestNode - INFO - [process:414] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP RESPONSE] Status: 404, URL: https://www.postb.in/1748518229727-4452951683197, Method: POST, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe 
2025-05-29 17:31:59 - ApiRequestNode - INFO - [process:419] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:01:58 GMT",
  "Content-Type": "text/html; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=de2E%2B86n6x6FQc0FwEHoxbrRq%2BofjEd6ZMxvv%2FSjJa43mRa5aC0R2xDz%2BaEsJiGmtH52j3Pa62PBWu%2BxomvgU0oct%2BSpAzfxNPWaMw%2F6FmD7Hx4u%2B56fiA%3D%3D\"}]}",
  "X-Response-Time": "0.33478ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475d714dd6be228-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ApiRequestNode - INFO - [process:430] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP RESPONSE CONTENT] Type: text/html, Length: unknown, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ApiRequestNode - INFO - [process:588] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] [HTTP RESPONSE BODY] Text: 404 - Not Found
, RequestID: 15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ApiRequestNode - WARNING - [process:657] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] API request failed (Client Error): Status=404 (Not Found), RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe, Error: API request failed with status 404 (Not Found): 404 - Not Found

2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:206] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Starting API request processing for request_id: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:224] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:232] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:234] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:240] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:260] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Final request body values - raw: {'technical': '12'}, json: None for request_id 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:267] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Request parameters extracted for request_id 45369c3d-1882-4678-bffc-f457c37b70e9: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:345] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:361] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:391] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:392] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP BODY] None
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:393] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:409] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST COMPLETED] Duration: 0.751s, Status: 200, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:414] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9 
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:419] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:05:14 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-g0TpDRY4gUYKDzMQrfXxGAASrvM\"",
  "X-Response-Time": "0.73879ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yqyJ424MEX37MNkWGugM8KgsSykbQWwqR3VDDlg7WO5vLIrgnznNmy%2BQct%2FQHFeQ3zEBOEylxUtQVWPB%2FgbHx%2BRjaWPz3J5FZghaUNcejHjuS2WNykYCLg%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475dbdc0c12acfb-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:430] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:588] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE BODY] Text: 1748520314529-8490416419226, RequestID: 45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ApiRequestNode - INFO - [process:598] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] API request successful: Status=200, RequestID=45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:206] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Starting API request processing for request_id: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:224] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:232] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:234] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:240] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:260] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Final request body values - raw: {'technical': '12'}, json: None for request_id 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:267] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Request parameters extracted for request_id 1c397645-ceac-45ff-b82e-27ea455d7466: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:345] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:361] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:391] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:392] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP BODY] None
2025-05-29 17:35:23 - ApiRequestNode - INFO - [process:393] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:409] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP REQUEST COMPLETED] Duration: 0.702s, Status: 200, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:414] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466 
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:419] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:05:24 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-K6kXbOVInJQCdEHhpU+cSie2pOE\"",
  "X-Response-Time": "0.66171ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=tlAo3TZFeX2w%2F5RaTeYl9Ur5xNSoGG2KERq14LOJGTKzeBRB4HBLNESBjjoxKxwlmICg6hAlci3Z9x4XajB2Zm2LUrN3vj2nG6aZdE2WKAQNycxwq082Pg%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475dc18cbf7e214-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:430] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:588] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] [HTTP RESPONSE BODY] Text: 1748520324242-9349877864588, RequestID: 1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ApiRequestNode - INFO - [process:598] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] API request successful: Status=200, RequestID=1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:206] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Starting API request processing for request_id: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:224] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:232] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:234] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:240] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:260] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Final request body values - raw: {'technical': '12'}, json: None for request_id 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:267] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Request parameters extracted for request_id 8089e511-3725-44b9-b6e6-3dab97eb428d: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:345] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:361] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:391] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:392] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP BODY] None
2025-05-29 17:42:14 - ApiRequestNode - INFO - [process:393] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:409] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST COMPLETED] Duration: 0.631s, Status: 200, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:414] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d 
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:419] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:12:14 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-C4A+S3DSkkf547mKeXMRuMNy3QU\"",
  "X-Response-Time": "0.84176ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=xAmH4N8fpj4cfM%2BmjcbBLoyrbocqecGpg6hSUHp6HN6zYtP09KCUOcnHj52fySkPyBm8ThgY2aq0f6qmBmaPU9ysEW0looFrtfXLFiZnPRWN20fzZreUSw%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475e61e7fb7e5bf-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:430] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:588] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE BODY] Text: 1748520734540-8572188343387, RequestID: 8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ApiRequestNode - INFO - [process:598] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] API request successful: Status=200, RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:206] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Starting API request processing for request_id: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:224] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:232] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:234] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:240] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:260] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Final request body values - raw: {'technical': '12'}, json: None for request_id f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:267] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Request parameters extracted for request_id f3248533-d896-4d3d-9aeb-f33a33ae02b8: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:345] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:361] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:391] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:392] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP BODY] None
2025-05-29 17:42:23 - ApiRequestNode - INFO - [process:393] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:409] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP REQUEST COMPLETED] Duration: 0.642s, Status: 200, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:414] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8 
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:419] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:12:23 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-2tLibyLkWDkYgCv6Yyl7o/5mx2Y\"",
  "X-Response-Time": "0.61744ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=o7AANdoipmwwV178j6%2Bfmb4xj8WXBZs9Xt5ujGr0MGCHaZ61hVzST0xCLKp99z4XJqq3NPbAKcwQZ0M7smvMPaiZ4loVf%2Bx%2BIv13HWpgE0qzRuuYA6YSdA%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475e658f804e1f8-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:430] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:588] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] [HTTP RESPONSE BODY] Text: 1748520743903-9707870695274, RequestID: f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ApiRequestNode - INFO - [process:598] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] API request successful: Status=200, RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:206] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Starting API request processing for request_id: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:224] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:232] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:234] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:240] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:260] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Final request body values - raw: {'technical': '12'}, json: None for request_id 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:267] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Request parameters extracted for request_id 4bc839a3-c1cf-4a86-a512-cc36eba4fc43: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:345] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:361] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:391] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:392] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP BODY] None
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:393] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:409] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST COMPLETED] Duration: 0.790s, Status: 200, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:414] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43 
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:419] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:15:35 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-O2F9PbPoiY+lVeYo+3xT5CbzmiQ\"",
  "X-Response-Time": "0.65551ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=l42CR%2BRM5kjZZXocERvPVR9CT061iHY5QMuvN4W63lWgHO4Nh6LQ2A9HDX8vKkvyzDlt7Odxw6lDQD6jUGo7%2FqnLLSDr5ToUFzjYYbQY7OzMEDaeuTc6Nw%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475eb045fbce1c1-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:430] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:588] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE BODY] Text: 1748520935375-5106869211886, RequestID: 4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ApiRequestNode - INFO - [process:598] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] API request successful: Status=200, RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:206] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Starting API request processing for request_id: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:224] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Extracting request new parameters https://www.postb.in/1748520220048-3774701473303, POST, {}, {} for request_id b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:232] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Detected dual-purpose input wrapper for 'body'. Extracting value from: {'value': '{"technical":"12"}'}
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:234] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Extracted body value: {"technical":"12"} (type: <class 'str'>)
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:240] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully parsed JSON string body: {'technical': '12'}
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:260] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Final request body values - raw: {'technical': '12'}, json: None for request_id b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:267] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Request parameters extracted for request_id b23bd17d-ed71-479c-8f5e-53fa77244c29: URL=https://www.postb.in/1748520220048-3774701473303, Method=POST, Timeout=None , MaxContentLength=1000000, TargetComponent=None
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:345] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST START] Method: POST, URL: https://www.postb.in/1748520220048-3774701473303, Timeout: Nones, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:361] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST BODY] JSON: {
  "technical": "12"
}, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:391] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP Method]: POST,[HTTP URL] : https://www.postb.in/1748520220048-3774701473303 RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:392] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP BODY] None
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:393] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP JSON BODY] {'technical': '12'}
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:409] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP REQUEST COMPLETED] Duration: 0.491s, Status: 200, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:414] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE] Status: 200, URL: https://www.postb.in/1748520220048-3774701473303, Method: POST, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29 
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:419] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE HEADERS] {
  "Date": "Thu, 29 May 2025 12:15:44 GMT",
  "Content-Type": "text/plain; charset=utf-8",
  "Transfer-Encoding": "chunked",
  "Connection": "keep-alive",
  "Etag": "W/\"1b-xIwp1mqwLq0OF+qf9vn8hJAA0CY\"",
  "X-Response-Time": "0.54968ms",
  "Via": "1.1 google",
  "Cf-Cache-Status": "DYNAMIC",
  "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
  "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=F0OfYZ3Du1n1tfo1GZNUdprv0FzhkMbb5vb9pi92rtvU2xalDI7dQTb5IGSDh%2FybDUPIRpQxhqQYwZGsq%2Fv%2B0M%2FByjfd%2BTKVQG5T4q9SJHwxb7S7vs3VHQ%3D%3D\"}]}",
  "Content-Encoding": "gzip",
  "Server": "cloudflare",
  "CF-RAY": "9475eb3c0fb9e161-MRS",
  "alt-svc": "h3=\":443\"; ma=86400"
}, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:430] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE CONTENT] Type: text/plain, Length: unknown, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:588] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] [HTTP RESPONSE BODY] Text: 1748520944077-4253199549857, RequestID: b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ApiRequestNode - INFO - [process:598] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] API request successful: Status=200, RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29
