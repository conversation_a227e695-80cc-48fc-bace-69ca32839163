2025-05-29 17:58:36 - ToolExecutor - INFO - [setup_logger:467] Logger ToolExecutor configured with log file: logs\2025-05-29\ToolExecutor_17-58-36.log
2025-05-29 17:58:36 - ToolExecutor - INFO - [setup_tool_executor_logger:97] Kafka logging enabled for ToolExecutor, sending to dedicated topic: tool_executor_logs
2025-05-29 17:58:36 - ToolExecutor - INFO - [get_tool_executor:281] Creating new global ToolExecutor instance
2025-05-29 17:58:36 - ToolExecutor - INFO - [__init__:76] Initializing ToolExecutor
2025-05-29 17:58:36 - Too<PERSON>Executor - INFO - [__init__:78] ToolExecutor initialized successfully
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:94] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:97] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:116] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool name: MergeDataComponent for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:151] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing payload with component MergeDataComponent for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:155] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Component MergeDataComponent processed payload successfully for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - ToolExecutor - INFO - [execute_tool:186] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] ToolExecutor returning error from component: {
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool for request_id: 2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:116] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool name: CombineTextComponent for request_id: 2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:151] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Processing payload with component CombineTextComponent for request_id: 2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:155] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Component CombineTextComponent processed payload successfully for request_id: 2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:225] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor returning success: {
  "request_id": "2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c",
  "status": "success",
  "result": {
    "status": "success",
    "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
  }
}
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:94] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool for request_id: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:97] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "ed88ea01-bdeb-498d-ae87-c4470fd24846",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:116] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool name: ApiRequestNode for request_id: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:49 - ToolExecutor - INFO - [execute_tool:151] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Processing payload with component ApiRequestNode for request_id: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ToolExecutor - INFO - [execute_tool:155] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Component ApiRequestNode processed payload successfully for request_id: ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ToolExecutor - INFO - [execute_tool:225] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "ed88ea01-bdeb-498d-ae87-c4470fd24846",
  "status": "success",
  "response": {
    "result": "1748521789747-3573702345602",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:29:49 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-At2G18Fo1BORqGP69J6pfvEj7hY\"",
      "X-Response-Time": "0.72641ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=ihvBiP6jHrcxgiTIc6JAbRcFe0TuocUpxAWOjjI%2F7YJBRQ1BladlQwQR7hawk4%2FN0Go4CUDjEmRbzz9hru826caOdK%2BGjT5Sba52c1ApPkj8in9JowU%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "9475ffe018be1e71-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:94] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool for request_id: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:97] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor received payload: {
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "d8efe309-1471-4867-960e-a6d88e5c41bd",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:116] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool name: ApiRequestNode for request_id: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:151] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Processing payload with component ApiRequestNode for request_id: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:94] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool for request_id: 421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:97] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor received payload: {
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "421f02cc-5aab-4876-a4a9-ba023acf34e9",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:116] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool name: MergeDataComponent for request_id: 421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:151] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Processing payload with component MergeDataComponent for request_id: 421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:155] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Component MergeDataComponent processed payload successfully for request_id: 421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ToolExecutor - INFO - [execute_tool:186] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor returning error from component: {
  "request_id": "421f02cc-5aab-4876-a4a9-ba023acf34e9",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:59:58 - ToolExecutor - INFO - [execute_tool:155] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Component ApiRequestNode processed payload successfully for request_id: d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ToolExecutor - INFO - [execute_tool:225] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] ToolExecutor returning success: {
  "component_type": "ApiRequestNode",
  "request_id": "d8efe309-1471-4867-960e-a6d88e5c41bd",
  "status": "success",
  "response": {
    "result": "1748521797587-9642807936761",
    "status_code": 200,
    "response_headers": {
      "Date": "Thu, 29 May 2025 12:29:57 GMT",
      "Content-Type": "text/plain; charset=utf-8",
      "Transfer-Encoding": "chunked",
      "Connection": "keep-alive",
      "Etag": "W/\"1b-ih666MxzFe3W0bHng253rPSegVI\"",
      "X-Response-Time": "0.64883ms",
      "Via": "1.1 google",
      "Cf-Cache-Status": "DYNAMIC",
      "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
      "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yQYAQRMzh4bVY%2FrAUKsRdvpTyFtVIFERm%2Buso03r5zlIOdapf7jlfB%2BpKpZ1Pc5dCsjUoohxHJ4S88Oqld0EkGeCw%2B64aev6hXEwMpvXjHoURf5ZIA0%3D\"}]}",
      "Content-Encoding": "gzip",
      "Server": "cloudflare",
      "CF-RAY": "947600111962e112-MRS",
      "alt-svc": "h3=\":443\"; ma=86400"
    },
    "error": null,
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST"
  }
}
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:94] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Executing tool for request_id: 1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:97] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "mine",
    "num_additional_inputs": "1",
    "separator": null,
    "input_1": "hello",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "1a31f1e9-9a7f-470a-bd24-ca525063aaa9",
  "correlation_id": "1669f02d-1670-49d2-b3fe-67e3f2deb5a3"
}
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:116] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Tool name: CombineTextComponent for request_id: 1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:151] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Processing payload with component CombineTextComponent for request_id: 1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:155] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Component CombineTextComponent processed payload successfully for request_id: 1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:48 - ToolExecutor - INFO - [execute_tool:186] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] ToolExecutor returning error from component: {
  "request_id": "1a31f1e9-9a7f-470a-bd24-ca525063aaa9",
  "status": "error",
  "result": {
    "error": "Error combining text for request_id 1a31f1e9-9a7f-470a-bd24-ca525063aaa9: 'NoneType' object has no attribute 'replace'"
  }
}
