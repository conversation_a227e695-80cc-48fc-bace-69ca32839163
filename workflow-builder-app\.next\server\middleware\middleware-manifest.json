{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\.[\\w]+$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "8e004f55ea24c0c56a8e8c95281afc25", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "33a7bf99db8d3b2231c4cbb1305e863113898dbcfbd35b728b4e1222119215b7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "64053a1a78db2e42a3396d29c1ad818c851973b729f3ad6e890fce8846d3b85f"}}}, "instrumentation": null, "functions": {}}