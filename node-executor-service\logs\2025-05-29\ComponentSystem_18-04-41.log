2025-05-29 18:04:41 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_18-04-41.log
2025-05-29 18:04:41 - ComponentSystem - INFO - [get_component_manager:1387] Creating new global ComponentManager instance
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 18:04:41 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_component_modules:1326] Discovering component modules
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_component_modules:1342] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 18:04:41 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.alter_metadata_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.api_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.api_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.combine_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.combine_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.combine_text_component_new
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.convert_script_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.data_to_dataframe_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.doc_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.doc_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.gmail_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.gmail_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.gmail_tracker_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.id_generator_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.id_generator_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.merge_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 18:04:42 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.merge_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.message_to_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.message_to_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.select_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.select_data_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.split_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.split_text_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.text_analysis_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.text_analysis_component
2025-05-29 18:04:42 - ComponentSystem - INFO - [discover_component_modules:1365] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 18:04:42 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 18:04:48 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 18:04:48 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 18:04:48 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 18:05:07 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=345, TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034
2025-05-29 18:05:07 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "mine",
    "num_additional_inputs": "1",
    "separator": ",",
    "input_1": "hello",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "3aa39dcf-7b54-449b-a117-55bbd528565e",
  "correlation_id": "4479e474-b0b0-4d50-b684-460961a3f99c"
}
2025-05-29 18:05:07 - ComponentSystem - INFO - [_process_message:713] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Executing tool CombineTextComponent for RequestID=3aa39dcf-7b54-449b-a117-55bbd528565e, TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034
2025-05-29 18:05:07 - ComponentSystem - INFO - [_process_message:717] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Tool CombineTextComponent executed successfully for RequestID=3aa39dcf-7b54-449b-a117-55bbd528565e, TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034
2025-05-29 18:05:07 - ComponentSystem - INFO - [_send_result:1005] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Preparing to send result for component ApiRequestNode, RequestID=3aa39dcf-7b54-449b-a117-55bbd528565e
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:244] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:247] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c]   Bootstrap Servers: **************:9092
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:248] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:252] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c]   Request Timeout: 60000ms
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:255] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 18:05:07 - ComponentSystem - INFO - [get_producer:259] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 18:05:09 - ComponentSystem - INFO - [get_producer:266] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 18:05:09 - ComponentSystem - INFO - [_send_result:1078] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Sending Kafka response: RequestID=3aa39dcf-7b54-449b-a117-55bbd528565e, Response={
  "request_id": "3aa39dcf-7b54-449b-a117-55bbd528565e",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748522109.2919416,
  "result": {
    "request_id": "3aa39dcf-7b54-449b-a117-55bbd528565e",
    "result": {
      "status": "success",
      "result": "mine,hello"
    }
  },
  "error": null
}
2025-05-29 18:05:09 - ComponentSystem - INFO - [_send_result:1087] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Sent result for component ApiRequestNode to topic node_results for RequestID=3aa39dcf-7b54-449b-a117-55bbd528565e
2025-05-29 18:05:09 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Successfully committed offset 346 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034
2025-05-29 18:05:09 - ComponentSystem - INFO - [_process_message:936] [ReqID:3aa39dcf-7b54-449b-a117-55bbd528565e] [CorrID:4479e474-b0b0-4d50-b684-460961a3f99c] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=345, TaskID=ApiRequestNode-node-execution-request-0-345-1748522107.3719034
2025-05-29 18:17:38 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-29 18:17:38 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-29 18:17:38 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-29 18:17:38 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
