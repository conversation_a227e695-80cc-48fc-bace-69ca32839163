2025-05-29 17:58:36 - MergeDataComponent - INFO - [setup_logger:467] Logger MergeDataComponent configured with log file: logs\2025-05-29\MergeDataComponent_17-58-36.log
2025-05-29 17:58:42 - MergeDataComponent - INFO - [__init__:96] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] MergeDataExecutor initialized
2025-05-29 17:58:42 - MergeDataComponent - INFO - [process:241] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Processing merge data request for request_id: 7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - MergeDataComponent - INFO - [process:243] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:58:42 - MergeDataComponent - INFO - [process:257] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:58:42 - MergeDataComponent - INFO - [process:277] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Merging data for request_id 7a321b15-3271-4842-bad7-8266c74a7d43. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:58:42 - MergeDataComponent - ERROR - [process:308] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 17:59:57 - MergeDataComponent - INFO - [process:241] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Processing merge data request for request_id: 421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - MergeDataComponent - INFO - [process:243] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:59:57 - MergeDataComponent - INFO - [process:257] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'merge_strategy', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 'input_6', 'input_7', 'input_8', 'input_9', 'input_10', 'request_id']
2025-05-29 17:59:57 - MergeDataComponent - INFO - [process:277] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Merging data for request_id 421f02cc-5aab-4876-a4a9-ba023acf34e9. Strategy: 'Deep Merge', Num additional inputs: 1
2025-05-29 17:59:57 - MergeDataComponent - ERROR - [process:308] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
