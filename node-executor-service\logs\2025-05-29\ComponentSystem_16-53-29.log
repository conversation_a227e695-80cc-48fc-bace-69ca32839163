2025-05-29 16:53:29 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_16-53-29.log
2025-05-29 16:53:29 - ComponentSystem - INFO - [get_component_manager:1421] Creating new global ComponentManager instance
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 16:53:29 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1360] Discovering component modules
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1376] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.alter_metadata_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.api_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.api_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.combine_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component_new
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.convert_script_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.data_to_dataframe_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.doc_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.doc_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.gmail_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_tracker_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.id_generator_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.id_generator_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.merge_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 16:53:29 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.merge_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.message_to_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.message_to_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.select_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.select_data_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.split_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.split_text_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.text_analysis_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1390] Successfully imported component module: app.components.text_analysis_component
2025-05-29 16:53:29 - ComponentSystem - INFO - [discover_component_modules:1399] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 16:53:29 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 16:53:36 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 16:53:36 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 16:53:36 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 17:01:54 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=316, TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019
2025-05-29 17:01:54 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "4156040d-38a6-49db-b202-d383397630ea",
  "correlation_id": "b85e16bc-2757-44fe-a6b3-e0f4e3a55c01"
}
2025-05-29 17:01:54 - ComponentSystem - INFO - [_process_message:713] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Executing tool ApiRequestNode for RequestID=4156040d-38a6-49db-b202-d383397630ea, TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019
2025-05-29 17:01:55 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=317, TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658
2025-05-29 17:01:55 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": null,
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "a75efa9f-6545-4261-ac84-aba74622d7ea",
  "correlation_id": "b85e16bc-2757-44fe-a6b3-e0f4e3a55c01"
}
2025-05-29 17:01:55 - ComponentSystem - INFO - [_process_message:713] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Executing tool MergeDataComponent for RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea, TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658
2025-05-29 17:01:55 - ComponentSystem - INFO - [_process_message:717] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Tool MergeDataComponent executed successfully for RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea, TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658
2025-05-29 17:01:55 - ComponentSystem - INFO - [_send_result:1005] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Preparing to send result for component ApiRequestNode, RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:244] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:247] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Bootstrap Servers: **************:9092
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:248] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:252] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Request Timeout: 60000ms
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:255] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:259] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 17:01:55 - ComponentSystem - INFO - [_process_message:717] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Tool ApiRequestNode executed successfully for RequestID=4156040d-38a6-49db-b202-d383397630ea, TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019
2025-05-29 17:01:55 - ComponentSystem - INFO - [_send_result:1005] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Preparing to send result for component ApiRequestNode, RequestID=4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:244] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:247] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Bootstrap Servers: **************:9092
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:248] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:252] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Request Timeout: 60000ms
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:255] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 17:01:55 - ComponentSystem - INFO - [get_producer:259] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 17:01:57 - ComponentSystem - INFO - [get_producer:266] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 17:01:57 - ComponentSystem - INFO - [_send_result:1030] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Result contains error status or error field for RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:57 - ComponentSystem - INFO - [_send_result:1118] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Sending Kafka response: RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea, Response={
  "request_id": "a75efa9f-6545-4261-ac84-aba74622d7ea",
  "status": "error",
  "result": {
    "error": "Unknown merge strategy for request_id a75efa9f-6545-4261-ac84-aba74622d7ea: None"
  }
}
2025-05-29 17:01:57 - ComponentSystem - INFO - [_send_result:1127] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Sent result for component ApiRequestNode to topic node_results for RequestID=a75efa9f-6545-4261-ac84-aba74622d7ea
2025-05-29 17:01:57 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Successfully committed offset 318 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658
2025-05-29 17:01:57 - ComponentSystem - INFO - [_process_message:936] [ReqID:a75efa9f-6545-4261-ac84-aba74622d7ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=317, TaskID=ApiRequestNode-node-execution-request-0-317-1748518314.8343658
2025-05-29 17:01:57 - ComponentSystem - INFO - [get_producer:266] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 17:01:57 - ComponentSystem - INFO - [_send_result:1118] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Sending Kafka response: RequestID=4156040d-38a6-49db-b202-d383397630ea, Response={
  "request_id": "4156040d-38a6-49db-b202-d383397630ea",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "4156040d-38a6-49db-b202-d383397630ea",
    "status": "success",
    "response": {
      "result": "1748518315543-9699916734825",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 11:31:55 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-Coxo3/byphWYxA+8pyAdVjMvcpc\"",
        "X-Response-Time": "0.51569ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=kgIGji3HQ3OUxQwMHeLB1jVyShxCQC8pBlXpCBek5gPKJkCeM36QwU5%2BxlcRy0z5uP2V3mxIXbdGbbx1FPfA0Kkgq9lpb%2B%2FT96XB2cbAxYxjq2Kdi1o%2FEA%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475ab0e6b4fe161-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748518229727-4452951683197",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748518317.935478
}
2025-05-29 17:01:58 - ComponentSystem - INFO - [_send_result:1127] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Sent result for component ApiRequestNode to topic node_results for RequestID=4156040d-38a6-49db-b202-d383397630ea
2025-05-29 17:01:58 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Successfully committed offset 317 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019
2025-05-29 17:01:58 - ComponentSystem - INFO - [_process_message:936] [ReqID:4156040d-38a6-49db-b202-d383397630ea] [CorrID:b85e16bc-2757-44fe-a6b3-e0f4e3a55c01] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=316, TaskID=ApiRequestNode-node-execution-request-0-316-1748518314.824019
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=318, TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "eb7c4840-2e2c-45ff-8b32-5f289a5167ce",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:713] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool ApiRequestNode for RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce, TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=319, TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "20dedc4d-6b19-4a22-86fe-a73e762633c9",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:713] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool MergeDataComponent for RequestID=20dedc4d-6b19-4a22-86fe-a73e762633c9, TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468
2025-05-29 17:04:08 - ComponentSystem - INFO - [_process_message:717] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool MergeDataComponent executed successfully for RequestID=20dedc4d-6b19-4a22-86fe-a73e762633c9, TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468
2025-05-29 17:04:08 - ComponentSystem - INFO - [_send_result:1005] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Preparing to send result for component ApiRequestNode, RequestID=20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:08 - ComponentSystem - INFO - [_send_result:1118] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sending Kafka response: RequestID=20dedc4d-6b19-4a22-86fe-a73e762633c9, Response={
  "request_id": "20dedc4d-6b19-4a22-86fe-a73e762633c9",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "20dedc4d-6b19-4a22-86fe-a73e762633c9",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new\":\"112\"}",
        "social": "5"
      }
    }
  },
  "status": "success",
  "timestamp": 1748518448.9305215
}
2025-05-29 17:04:09 - ComponentSystem - INFO - [_send_result:1127] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sent result for component ApiRequestNode to topic node_results for RequestID=20dedc4d-6b19-4a22-86fe-a73e762633c9
2025-05-29 17:04:09 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully committed offset 320 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468
2025-05-29 17:04:09 - ComponentSystem - INFO - [_process_message:936] [ReqID:20dedc4d-6b19-4a22-86fe-a73e762633c9] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=319, TaskID=ApiRequestNode-node-execution-request-0-319-1748518448.9144468
2025-05-29 17:04:09 - ComponentSystem - INFO - [_process_message:717] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool ApiRequestNode executed successfully for RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce, TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452
2025-05-29 17:04:09 - ComponentSystem - INFO - [_send_result:1005] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Preparing to send result for component ApiRequestNode, RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:09 - ComponentSystem - INFO - [_send_result:1118] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sending Kafka response: RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce, Response={
  "request_id": "eb7c4840-2e2c-45ff-8b32-5f289a5167ce",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "eb7c4840-2e2c-45ff-8b32-5f289a5167ce",
    "status": "success",
    "response": {
      "result": "1748518449180-7919062976725",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 11:34:09 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-Wg55KgkBTPfolaGIcnCfH2ztqnA\"",
        "X-Response-Time": "0.63825ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=uBNcG%2FLovATJkOsVTYy2OuEbAwUh%2BGQoO22F728W9XzbH8B3FWZrUVGEwmnvKOpRP%2BrHQ5GjS%2F7nsK5JWz6jrygUEUR7F3PrfsAD6eC71PoM1rRBiP20VQ%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475ae519b08e1fd-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748518229727-4452951683197",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748518449.6076686
}
2025-05-29 17:04:09 - ComponentSystem - INFO - [_send_result:1127] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sent result for component ApiRequestNode to topic node_results for RequestID=eb7c4840-2e2c-45ff-8b32-5f289a5167ce
2025-05-29 17:04:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully committed offset 319 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452
2025-05-29 17:04:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:eb7c4840-2e2c-45ff-8b32-5f289a5167ce] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=318, TaskID=ApiRequestNode-node-execution-request-0-318-1748518448.9134452
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=320, TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "ec2122d4-c3b0-4386-a592-683439438d95",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:713] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool ApiRequestNode for RequestID=ec2122d4-c3b0-4386-a592-683439438d95, TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=321, TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "53adf93a-c760-48fc-a088-529088348acd",
  "correlation_id": "4c778827-2d5b-41df-9324-8bcafabb4141"
}
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:713] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Executing tool MergeDataComponent for RequestID=53adf93a-c760-48fc-a088-529088348acd, TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:717] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool MergeDataComponent executed successfully for RequestID=53adf93a-c760-48fc-a088-529088348acd, TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106
2025-05-29 17:04:18 - ComponentSystem - INFO - [_send_result:1005] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Preparing to send result for component ApiRequestNode, RequestID=53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ComponentSystem - INFO - [_send_result:1118] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sending Kafka response: RequestID=53adf93a-c760-48fc-a088-529088348acd, Response={
  "request_id": "53adf93a-c760-48fc-a088-529088348acd",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "53adf93a-c760-48fc-a088-529088348acd",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new\":\"112\"}",
        "media": "help"
      }
    }
  },
  "status": "success",
  "timestamp": 1748518458.2388623
}
2025-05-29 17:04:18 - ComponentSystem - INFO - [_send_result:1127] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sent result for component ApiRequestNode to topic node_results for RequestID=53adf93a-c760-48fc-a088-529088348acd
2025-05-29 17:04:18 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully committed offset 322 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:936] [ReqID:53adf93a-c760-48fc-a088-529088348acd] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=321, TaskID=ApiRequestNode-node-execution-request-0-321-1748518458.1969106
2025-05-29 17:04:18 - ComponentSystem - INFO - [_process_message:717] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Tool ApiRequestNode executed successfully for RequestID=ec2122d4-c3b0-4386-a592-683439438d95, TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591
2025-05-29 17:04:18 - ComponentSystem - INFO - [_send_result:1005] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Preparing to send result for component ApiRequestNode, RequestID=ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:18 - ComponentSystem - INFO - [_send_result:1118] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sending Kafka response: RequestID=ec2122d4-c3b0-4386-a592-683439438d95, Response={
  "request_id": "ec2122d4-c3b0-4386-a592-683439438d95",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "ec2122d4-c3b0-4386-a592-683439438d95",
    "status": "success",
    "response": {
      "result": "1748518458479-5113869060296",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 11:34:18 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-l8lKaC21Vesm5oba7Fk4dcWQqZs\"",
        "X-Response-Time": "0.49125ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=80qxtokT5vxAS3oOT7hk4m6n%2FDB4pKcIZ5RCWXkPBXPi1FLqnnoafjI1TqcuW8UaJKRfq%2F%2FzmE3V9nvcTb%2BmydabYu80Zm1c8SAW6%2BnLMi85fw11pgYOXQ%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475ae8bce3ce17b-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748518229727-4452951683197",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748518458.9066942
}
2025-05-29 17:04:19 - ComponentSystem - INFO - [_send_result:1127] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Sent result for component ApiRequestNode to topic node_results for RequestID=ec2122d4-c3b0-4386-a592-683439438d95
2025-05-29 17:04:19 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Successfully committed offset 321 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591
2025-05-29 17:04:19 - ComponentSystem - INFO - [_process_message:936] [ReqID:ec2122d4-c3b0-4386-a592-683439438d95] [CorrID:4c778827-2d5b-41df-9324-8bcafabb4141] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=320, TaskID=ApiRequestNode-node-execution-request-0-320-1748518458.19591
2025-05-29 17:10:15 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=322, TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363
2025-05-29 17:10:15 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "315f43ec-5f25-47ca-a36e-9d853e41b475",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:15 - ComponentSystem - INFO - [_process_message:713] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool ApiRequestNode for RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475, TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=323, TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:713] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool MergeDataComponent for RequestID=6f1f3354-4496-401b-9842-4fcdc004df99, TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:717] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool MergeDataComponent executed successfully for RequestID=6f1f3354-4496-401b-9842-4fcdc004df99, TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049
2025-05-29 17:10:16 - ComponentSystem - INFO - [_send_result:1005] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Preparing to send result for component ApiRequestNode, RequestID=6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ComponentSystem - INFO - [_send_result:1118] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sending Kafka response: RequestID=6f1f3354-4496-401b-9842-4fcdc004df99, Response={
  "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "6f1f3354-4496-401b-9842-4fcdc004df99",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new\":\"112\"}",
        "social": "5"
      }
    }
  },
  "status": "success",
  "timestamp": 1748518816.1119394
}
2025-05-29 17:10:16 - ComponentSystem - INFO - [_send_result:1127] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sent result for component ApiRequestNode to topic node_results for RequestID=6f1f3354-4496-401b-9842-4fcdc004df99
2025-05-29 17:10:16 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully committed offset 324 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:936] [ReqID:6f1f3354-4496-401b-9842-4fcdc004df99] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=323, TaskID=ApiRequestNode-node-execution-request-0-323-1748518815.927049
2025-05-29 17:10:16 - ComponentSystem - INFO - [_process_message:717] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool ApiRequestNode executed successfully for RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475, TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363
2025-05-29 17:10:16 - ComponentSystem - INFO - [_send_result:1005] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Preparing to send result for component ApiRequestNode, RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:16 - ComponentSystem - INFO - [_send_result:1118] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sending Kafka response: RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475, Response={
  "request_id": "315f43ec-5f25-47ca-a36e-9d853e41b475",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "315f43ec-5f25-47ca-a36e-9d853e41b475",
    "status": "success",
    "response": {
      "result": "1748518816428-6256029747892",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 11:40:16 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-9+pwHn8MwsTiIIwmpJHaq4Qqnu8\"",
        "X-Response-Time": "0.62231ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=bIAJGO5z6IQy6aaR9Bt%2B3QZNJ%2BDi0ayJ9N%2BeYMElG5IDxKWccdOE11qbhw8yonwrLJesUofa5sJnXYSWxLuaIxx8%2FXkPfK0Eq9fclQ5bFMox4rfqYONTtg%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475b748f9f7e1cc-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748518229727-4452951683197",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748518816.8849368
}
2025-05-29 17:10:17 - ComponentSystem - INFO - [_send_result:1127] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sent result for component ApiRequestNode to topic node_results for RequestID=315f43ec-5f25-47ca-a36e-9d853e41b475
2025-05-29 17:10:17 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully committed offset 323 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363
2025-05-29 17:10:17 - ComponentSystem - INFO - [_process_message:936] [ReqID:315f43ec-5f25-47ca-a36e-9d853e41b475] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=322, TaskID=ApiRequestNode-node-execution-request-0-322-1748518815.9230363
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=324, TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "bdcfd532-d649-4cde-a293-019db805555a",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:713] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool ApiRequestNode for RequestID=bdcfd532-d649-4cde-a293-019db805555a, TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=325, TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "4fbc78b5-9b1b-4357-ab42-407864302c0d",
  "correlation_id": "9d68ce08-221d-44de-a902-54cd00278283"
}
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:713] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Executing tool MergeDataComponent for RequestID=4fbc78b5-9b1b-4357-ab42-407864302c0d, TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool MergeDataComponent executed successfully for RequestID=4fbc78b5-9b1b-4357-ab42-407864302c0d, TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Preparing to send result for component ApiRequestNode, RequestID=4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sending Kafka response: RequestID=4fbc78b5-9b1b-4357-ab42-407864302c0d, Response={
  "request_id": "4fbc78b5-9b1b-4357-ab42-407864302c0d",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "4fbc78b5-9b1b-4357-ab42-407864302c0d",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new\":\"112\"}",
        "media": "help"
      }
    }
  },
  "status": "success",
  "timestamp": 1748518824.121322
}
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sent result for component ApiRequestNode to topic node_results for RequestID=4fbc78b5-9b1b-4357-ab42-407864302c0d
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Tool ApiRequestNode executed successfully for RequestID=bdcfd532-d649-4cde-a293-019db805555a, TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Preparing to send result for component ApiRequestNode, RequestID=bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sending Kafka response: RequestID=bdcfd532-d649-4cde-a293-019db805555a, Response={
  "request_id": "bdcfd532-d649-4cde-a293-019db805555a",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "bdcfd532-d649-4cde-a293-019db805555a",
    "status": "success",
    "response": {
      "result": "1748518824170-5158934467472",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 11:40:24 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-bIGiOAHo1yGDshmmJ0AJQyzz4Y8\"",
        "X-Response-Time": "0.57367ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=mcdgNi0rHEG%2F2JUjGkLnmVOFFbU9W0Srwmp9eKayQRDH0WU3l%2FX91hvIWSudPjrqVyg7CBvQw2T3Ns5oIK4LLifFZlvtB%2FvMJ7KKOQmk9FtAPPxBo%2BSPVA%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475b77a998be1cd-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748518229727-4452951683197",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748518824.6106112
}
2025-05-29 17:10:24 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully committed offset 326 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_process_message:936] [ReqID:4fbc78b5-9b1b-4357-ab42-407864302c0d] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=325, TaskID=ApiRequestNode-node-execution-request-0-325-1748518824.0845418
2025-05-29 17:10:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Sent result for component ApiRequestNode to topic node_results for RequestID=bdcfd532-d649-4cde-a293-019db805555a
2025-05-29 17:10:25 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Successfully committed offset 325 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418
2025-05-29 17:10:25 - ComponentSystem - INFO - [_process_message:936] [ReqID:bdcfd532-d649-4cde-a293-019db805555a] [CorrID:9d68ce08-221d-44de-a902-54cd00278283] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=324, TaskID=ApiRequestNode-node-execution-request-0-324-1748518824.0845418
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=326, TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "be6ecb6e-1c4a-4d66-9017-5852c2efc0d3",
  "correlation_id": "c64cbedd-a7f5-4f92-a473-f0ca789ecb0b"
}
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:713] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Executing tool MergeDataComponent for RequestID=be6ecb6e-1c4a-4d66-9017-5852c2efc0d3, TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:717] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Tool MergeDataComponent executed successfully for RequestID=be6ecb6e-1c4a-4d66-9017-5852c2efc0d3, TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816
2025-05-29 17:31:58 - ComponentSystem - INFO - [_send_result:1005] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Preparing to send result for component ApiRequestNode, RequestID=be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ComponentSystem - INFO - [_send_result:1118] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Sending Kafka response: RequestID=be6ecb6e-1c4a-4d66-9017-5852c2efc0d3, Response={
  "request_id": "be6ecb6e-1c4a-4d66-9017-5852c2efc0d3",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "be6ecb6e-1c4a-4d66-9017-5852c2efc0d3",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new_value\":\"112\"}",
        "social": "5"
      }
    }
  },
  "status": "success",
  "timestamp": 1748520118.2875044
}
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=327, TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748518229727-4452951683197",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "15c42e55-1b6b-4004-8650-5222caa8cabe",
  "correlation_id": "c64cbedd-a7f5-4f92-a473-f0ca789ecb0b"
}
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:713] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Executing tool ApiRequestNode for RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe, TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561
2025-05-29 17:31:58 - ComponentSystem - INFO - [_send_result:1127] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Sent result for component ApiRequestNode to topic node_results for RequestID=be6ecb6e-1c4a-4d66-9017-5852c2efc0d3
2025-05-29 17:31:58 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Successfully committed offset 327 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816
2025-05-29 17:31:58 - ComponentSystem - INFO - [_process_message:936] [ReqID:be6ecb6e-1c4a-4d66-9017-5852c2efc0d3] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=326, TaskID=ApiRequestNode-node-execution-request-0-326-1748520118.1870816
2025-05-29 17:31:59 - ComponentSystem - INFO - [_process_message:717] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Tool ApiRequestNode executed successfully for RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe, TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561
2025-05-29 17:31:59 - ComponentSystem - INFO - [_send_result:1005] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Preparing to send result for component ApiRequestNode, RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ComponentSystem - INFO - [_send_result:1030] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Result contains error status or error field for RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ComponentSystem - INFO - [_send_result:1118] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Sending Kafka response: RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe, Response={
  "request_id": "15c42e55-1b6b-4004-8650-5222caa8cabe",
  "status": "error",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "15c42e55-1b6b-4004-8650-5222caa8cabe",
    "response": {
      "result": "404 - Not Found\n",
      "status_code": 404,
      "response_headers": {},
      "error": "API request failed with status 404 (Not Found): 404 - Not Found\n"
    }
  }
}
2025-05-29 17:31:59 - ComponentSystem - INFO - [_send_result:1127] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Sent result for component ApiRequestNode to topic node_results for RequestID=15c42e55-1b6b-4004-8650-5222caa8cabe
2025-05-29 17:31:59 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Successfully committed offset 328 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561
2025-05-29 17:31:59 - ComponentSystem - INFO - [_process_message:936] [ReqID:15c42e55-1b6b-4004-8650-5222caa8cabe] [CorrID:c64cbedd-a7f5-4f92-a473-f0ca789ecb0b] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=327, TaskID=ApiRequestNode-node-execution-request-0-327-1748520118.192561
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=328, TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "social": "5"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "c3220f92-eb78-49a0-afca-1353ca12850b",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool MergeDataComponent for RequestID=c3220f92-eb78-49a0-afca-1353ca12850b, TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:717] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool MergeDataComponent executed successfully for RequestID=c3220f92-eb78-49a0-afca-1353ca12850b, TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383
2025-05-29 17:35:14 - ComponentSystem - INFO - [_send_result:1005] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Preparing to send result for component ApiRequestNode, RequestID=c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ComponentSystem - INFO - [_send_result:1118] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sending Kafka response: RequestID=c3220f92-eb78-49a0-afca-1353ca12850b, Response={
  "request_id": "c3220f92-eb78-49a0-afca-1353ca12850b",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "c3220f92-eb78-49a0-afca-1353ca12850b",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new_value\":\"112\"}",
        "social": "5"
      }
    }
  },
  "status": "success",
  "timestamp": 1748520314.2033532
}
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=329, TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "45369c3d-1882-4678-bffc-f457c37b70e9",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool ApiRequestNode for RequestID=45369c3d-1882-4678-bffc-f457c37b70e9, TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223
2025-05-29 17:35:14 - ComponentSystem - INFO - [_send_result:1127] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sent result for component ApiRequestNode to topic node_results for RequestID=c3220f92-eb78-49a0-afca-1353ca12850b
2025-05-29 17:35:14 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully committed offset 329 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:936] [ReqID:c3220f92-eb78-49a0-afca-1353ca12850b] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=328, TaskID=ApiRequestNode-node-execution-request-0-328-1748520314.1430383
2025-05-29 17:35:14 - ComponentSystem - INFO - [_process_message:717] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool ApiRequestNode executed successfully for RequestID=45369c3d-1882-4678-bffc-f457c37b70e9, TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223
2025-05-29 17:35:14 - ComponentSystem - INFO - [_send_result:1005] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Preparing to send result for component ApiRequestNode, RequestID=45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:14 - ComponentSystem - INFO - [_send_result:1118] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sending Kafka response: RequestID=45369c3d-1882-4678-bffc-f457c37b70e9, Response={
  "request_id": "45369c3d-1882-4678-bffc-f457c37b70e9",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "45369c3d-1882-4678-bffc-f457c37b70e9",
    "status": "success",
    "response": {
      "result": "1748520314529-8490416419226",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:05:14 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-g0TpDRY4gUYKDzMQrfXxGAASrvM\"",
        "X-Response-Time": "0.73879ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yqyJ424MEX37MNkWGugM8KgsSykbQWwqR3VDDlg7WO5vLIrgnznNmy%2BQct%2FQHFeQ3zEBOEylxUtQVWPB%2FgbHx%2BRjaWPz3J5FZghaUNcejHjuS2WNykYCLg%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475dbdc0c12acfb-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520314.9972706
}
2025-05-29 17:35:15 - ComponentSystem - INFO - [_send_result:1127] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sent result for component ApiRequestNode to topic node_results for RequestID=45369c3d-1882-4678-bffc-f457c37b70e9
2025-05-29 17:35:15 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully committed offset 330 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223
2025-05-29 17:35:15 - ComponentSystem - INFO - [_process_message:936] [ReqID:45369c3d-1882-4678-bffc-f457c37b70e9] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=329, TaskID=ApiRequestNode-node-execution-request-0-329-1748520314.1458223
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=330, TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "1c397645-ceac-45ff-b82e-27ea455d7466",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:713] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool ApiRequestNode for RequestID=1c397645-ceac-45ff-b82e-27ea455d7466, TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=331, TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": {
      "value": "{\"new_value\":\"112\"}"
    },
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "22049bfe-d09e-483e-9e4c-940c1955dbeb",
  "correlation_id": "51584db9-3192-4854-8f78-d16357337b58"
}
2025-05-29 17:35:23 - ComponentSystem - INFO - [_process_message:713] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Executing tool MergeDataComponent for RequestID=22049bfe-d09e-483e-9e4c-940c1955dbeb, TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148
2025-05-29 17:35:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool MergeDataComponent executed successfully for RequestID=22049bfe-d09e-483e-9e4c-940c1955dbeb, TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Preparing to send result for component ApiRequestNode, RequestID=22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sending Kafka response: RequestID=22049bfe-d09e-483e-9e4c-940c1955dbeb, Response={
  "request_id": "22049bfe-d09e-483e-9e4c-940c1955dbeb",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "22049bfe-d09e-483e-9e4c-940c1955dbeb",
    "status": "success",
    "result": {
      "status": "success",
      "result": {
        "value": "{\"new_value\":\"112\"}",
        "media": "help"
      }
    }
  },
  "status": "success",
  "timestamp": 1748520324.0044816
}
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sent result for component ApiRequestNode to topic node_results for RequestID=22049bfe-d09e-483e-9e4c-940c1955dbeb
2025-05-29 17:35:24 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully committed offset 332 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148
2025-05-29 17:35:24 - ComponentSystem - INFO - [_process_message:936] [ReqID:22049bfe-d09e-483e-9e4c-940c1955dbeb] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=331, TaskID=ApiRequestNode-node-execution-request-0-331-1748520323.9709148
2025-05-29 17:35:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Tool ApiRequestNode executed successfully for RequestID=1c397645-ceac-45ff-b82e-27ea455d7466, TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Preparing to send result for component ApiRequestNode, RequestID=1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sending Kafka response: RequestID=1c397645-ceac-45ff-b82e-27ea455d7466, Response={
  "request_id": "1c397645-ceac-45ff-b82e-27ea455d7466",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "1c397645-ceac-45ff-b82e-27ea455d7466",
    "status": "success",
    "response": {
      "result": "1748520324242-9349877864588",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:05:24 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-K6kXbOVInJQCdEHhpU+cSie2pOE\"",
        "X-Response-Time": "0.66171ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=tlAo3TZFeX2w%2F5RaTeYl9Ur5xNSoGG2KERq14LOJGTKzeBRB4HBLNESBjjoxKxwlmICg6hAlci3Z9x4XajB2Zm2LUrN3vj2nG6aZdE2WKAQNycxwq082Pg%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475dc18cbf7e214-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520324.698802
}
2025-05-29 17:35:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Sent result for component ApiRequestNode to topic node_results for RequestID=1c397645-ceac-45ff-b82e-27ea455d7466
2025-05-29 17:35:25 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Successfully committed offset 331 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148
2025-05-29 17:35:25 - ComponentSystem - INFO - [_process_message:936] [ReqID:1c397645-ceac-45ff-b82e-27ea455d7466] [CorrID:51584db9-3192-4854-8f78-d16357337b58] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=330, TaskID=ApiRequestNode-node-execution-request-0-330-1748520323.9709148
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=332, TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "a908d36a-21dd-435b-9132-1eec76057a34",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool CombineTextComponent for RequestID=a908d36a-21dd-435b-9132-1eec76057a34, TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:717] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool CombineTextComponent executed successfully for RequestID=a908d36a-21dd-435b-9132-1eec76057a34, TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286
2025-05-29 17:42:14 - ComponentSystem - INFO - [_send_result:1005] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Preparing to send result for component ApiRequestNode, RequestID=a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ComponentSystem - INFO - [_send_result:1118] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sending Kafka response: RequestID=a908d36a-21dd-435b-9132-1eec76057a34, Response={
  "request_id": "a908d36a-21dd-435b-9132-1eec76057a34",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "a908d36a-21dd-435b-9132-1eec76057a34",
    "status": "success",
    "result": {
      "status": "success",
      "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
    }
  },
  "status": "success",
  "timestamp": 1748520734.3442364
}
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=333, TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "8089e511-3725-44b9-b6e6-3dab97eb428d",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:713] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool ApiRequestNode for RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d, TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864
2025-05-29 17:42:14 - ComponentSystem - INFO - [_send_result:1127] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sent result for component ApiRequestNode to topic node_results for RequestID=a908d36a-21dd-435b-9132-1eec76057a34
2025-05-29 17:42:14 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully committed offset 333 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286
2025-05-29 17:42:14 - ComponentSystem - INFO - [_process_message:936] [ReqID:a908d36a-21dd-435b-9132-1eec76057a34] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=332, TaskID=ApiRequestNode-node-execution-request-0-332-1748520734.273286
2025-05-29 17:42:15 - ComponentSystem - INFO - [_process_message:717] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool ApiRequestNode executed successfully for RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d, TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864
2025-05-29 17:42:15 - ComponentSystem - INFO - [_send_result:1005] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Preparing to send result for component ApiRequestNode, RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ComponentSystem - INFO - [_send_result:1118] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sending Kafka response: RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d, Response={
  "request_id": "8089e511-3725-44b9-b6e6-3dab97eb428d",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "8089e511-3725-44b9-b6e6-3dab97eb428d",
    "status": "success",
    "response": {
      "result": "1748520734540-8572188343387",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:12:14 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-C4A+S3DSkkf547mKeXMRuMNy3QU\"",
        "X-Response-Time": "0.84176ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=xAmH4N8fpj4cfM%2BmjcbBLoyrbocqecGpg6hSUHp6HN6zYtP09KCUOcnHj52fySkPyBm8ThgY2aq0f6qmBmaPU9ysEW0looFrtfXLFiZnPRWN20fzZreUSw%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475e61e7fb7e5bf-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520735.036709
}
2025-05-29 17:42:15 - ComponentSystem - INFO - [_send_result:1127] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sent result for component ApiRequestNode to topic node_results for RequestID=8089e511-3725-44b9-b6e6-3dab97eb428d
2025-05-29 17:42:15 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully committed offset 334 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864
2025-05-29 17:42:15 - ComponentSystem - INFO - [_process_message:936] [ReqID:8089e511-3725-44b9-b6e6-3dab97eb428d] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=333, TaskID=ApiRequestNode-node-execution-request-0-333-1748520734.2762864
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=334, TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "f3248533-d896-4d3d-9aeb-f33a33ae02b8",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:713] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool ApiRequestNode for RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8, TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=335, TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "cc2f3744-c28e-4966-a637-e6d614f8e6d3",
  "correlation_id": "2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff"
}
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:713] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Executing tool MergeDataComponent for RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3, TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918
2025-05-29 17:42:23 - ComponentSystem - INFO - [_process_message:717] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool MergeDataComponent executed successfully for RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3, TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918
2025-05-29 17:42:23 - ComponentSystem - INFO - [_send_result:1005] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Preparing to send result for component ApiRequestNode, RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ComponentSystem - INFO - [_send_result:1030] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Result contains error status or error field for RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:23 - ComponentSystem - INFO - [_send_result:1118] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sending Kafka response: RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3, Response={
  "request_id": "cc2f3744-c28e-4966-a637-e6d614f8e6d3",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:42:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sent result for component ApiRequestNode to topic node_results for RequestID=cc2f3744-c28e-4966-a637-e6d614f8e6d3
2025-05-29 17:42:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Tool ApiRequestNode executed successfully for RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8, TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918
2025-05-29 17:42:24 - ComponentSystem - INFO - [_send_result:1005] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Preparing to send result for component ApiRequestNode, RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ComponentSystem - INFO - [_send_result:1118] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sending Kafka response: RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8, Response={
  "request_id": "f3248533-d896-4d3d-9aeb-f33a33ae02b8",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "f3248533-d896-4d3d-9aeb-f33a33ae02b8",
    "status": "success",
    "response": {
      "result": "1748520743903-9707870695274",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:12:23 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-2tLibyLkWDkYgCv6Yyl7o/5mx2Y\"",
        "X-Response-Time": "0.61744ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=o7AANdoipmwwV178j6%2Bfmb4xj8WXBZs9Xt5ujGr0MGCHaZ61hVzST0xCLKp99z4XJqq3NPbAKcwQZ0M7smvMPaiZ4loVf%2Bx%2BIv13HWpgE0qzRuuYA6YSdA%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475e658f804e1f8-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520744.365344
}
2025-05-29 17:42:24 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully committed offset 336 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918
2025-05-29 17:42:24 - ComponentSystem - INFO - [_process_message:936] [ReqID:cc2f3744-c28e-4966-a637-e6d614f8e6d3] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=335, TaskID=ApiRequestNode-node-execution-request-0-335-1748520743.7028918
2025-05-29 17:42:24 - ComponentSystem - INFO - [_send_result:1127] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Sent result for component ApiRequestNode to topic node_results for RequestID=f3248533-d896-4d3d-9aeb-f33a33ae02b8
2025-05-29 17:42:24 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Successfully committed offset 335 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918
2025-05-29 17:42:24 - ComponentSystem - INFO - [_process_message:936] [ReqID:f3248533-d896-4d3d-9aeb-f33a33ae02b8] [CorrID:2abcd9b9-3fde-4a9e-9f6d-47c0a0154dff] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=334, TaskID=ApiRequestNode-node-execution-request-0-334-1748520743.7028918
2025-05-29 17:45:34 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=336, TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "612859f2-5b34-4c50-b512-0a407f64eab3",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:713] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool CombineTextComponent for RequestID=612859f2-5b34-4c50-b512-0a407f64eab3, TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:717] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool CombineTextComponent executed successfully for RequestID=612859f2-5b34-4c50-b512-0a407f64eab3, TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688
2025-05-29 17:45:35 - ComponentSystem - INFO - [_send_result:1005] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Preparing to send result for component ApiRequestNode, RequestID=612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ComponentSystem - INFO - [_send_result:1118] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sending Kafka response: RequestID=612859f2-5b34-4c50-b512-0a407f64eab3, Response={
  "request_id": "612859f2-5b34-4c50-b512-0a407f64eab3",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "612859f2-5b34-4c50-b512-0a407f64eab3",
    "status": "success",
    "result": {
      "status": "success",
      "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
    }
  },
  "status": "success",
  "timestamp": 1748520935.0240014
}
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=337, TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "4bc839a3-c1cf-4a86-a512-cc36eba4fc43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:713] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool ApiRequestNode for RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43, TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685
2025-05-29 17:45:35 - ComponentSystem - INFO - [_send_result:1127] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sent result for component ApiRequestNode to topic node_results for RequestID=612859f2-5b34-4c50-b512-0a407f64eab3
2025-05-29 17:45:35 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully committed offset 337 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:936] [ReqID:612859f2-5b34-4c50-b512-0a407f64eab3] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=336, TaskID=ApiRequestNode-node-execution-request-0-336-1748520934.9641688
2025-05-29 17:45:35 - ComponentSystem - INFO - [_process_message:717] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool ApiRequestNode executed successfully for RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43, TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685
2025-05-29 17:45:35 - ComponentSystem - INFO - [_send_result:1005] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Preparing to send result for component ApiRequestNode, RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:35 - ComponentSystem - INFO - [_send_result:1118] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sending Kafka response: RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43, Response={
  "request_id": "4bc839a3-c1cf-4a86-a512-cc36eba4fc43",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "4bc839a3-c1cf-4a86-a512-cc36eba4fc43",
    "status": "success",
    "response": {
      "result": "1748520935375-5106869211886",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:15:35 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-O2F9PbPoiY+lVeYo+3xT5CbzmiQ\"",
        "X-Response-Time": "0.65551ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=l42CR%2BRM5kjZZXocERvPVR9CT061iHY5QMuvN4W63lWgHO4Nh6LQ2A9HDX8vKkvyzDlt7Odxw6lDQD6jUGo7%2FqnLLSDr5ToUFzjYYbQY7OzMEDaeuTc6Nw%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475eb045fbce1c1-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520935.8860064
}
2025-05-29 17:45:36 - ComponentSystem - INFO - [_send_result:1127] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sent result for component ApiRequestNode to topic node_results for RequestID=4bc839a3-c1cf-4a86-a512-cc36eba4fc43
2025-05-29 17:45:36 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully committed offset 338 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685
2025-05-29 17:45:36 - ComponentSystem - INFO - [_process_message:936] [ReqID:4bc839a3-c1cf-4a86-a512-cc36eba4fc43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=337, TaskID=ApiRequestNode-node-execution-request-0-337-1748520934.968685
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=338, TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "b23bd17d-ed71-479c-8f5e-53fa77244c29",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:713] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool ApiRequestNode for RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29, TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=339, TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:713] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool MergeDataComponent for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:717] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool MergeDataComponent executed successfully for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1005] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Preparing to send result for component ApiRequestNode, RequestID=7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1030] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Result contains error status or error field for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1118] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sending Kafka response: RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, Response={
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "status": "error",
  "result": {
    "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
  }
}
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1127] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sent result for component ApiRequestNode to topic node_results for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:717] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool ApiRequestNode executed successfully for RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29, TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1005] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Preparing to send result for component ApiRequestNode, RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1118] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sending Kafka response: RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29, Response={
  "request_id": "b23bd17d-ed71-479c-8f5e-53fa77244c29",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "b23bd17d-ed71-479c-8f5e-53fa77244c29",
    "status": "success",
    "response": {
      "result": "1748520944077-4253199549857",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:15:44 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-xIwp1mqwLq0OF+qf9vn8hJAA0CY\"",
        "X-Response-Time": "0.54968ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=F0OfYZ3Du1n1tfo1GZNUdprv0FzhkMbb5vb9pi92rtvU2xalDI7dQTb5IGSDh%2FybDUPIRpQxhqQYwZGsq%2Fv%2B0M%2FByjfd%2BTKVQG5T4q9SJHwxb7S7vs3VHQ%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475eb3c0fb9e161-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748520944.5452354
}
2025-05-29 17:45:44 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully committed offset 340 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_process_message:936] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=339, TaskID=ApiRequestNode-node-execution-request-0-339-1748520944.02441
2025-05-29 17:45:44 - ComponentSystem - INFO - [_send_result:1127] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sent result for component ApiRequestNode to topic node_results for RequestID=b23bd17d-ed71-479c-8f5e-53fa77244c29
2025-05-29 17:45:45 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully committed offset 339 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441
2025-05-29 17:45:45 - ComponentSystem - INFO - [_process_message:936] [ReqID:b23bd17d-ed71-479c-8f5e-53fa77244c29] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=338, TaskID=ApiRequestNode-node-execution-request-0-338-1748520944.02441
2025-05-29 17:54:27 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-29 17:54:27 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-29 17:54:27 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-29 17:54:27 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
