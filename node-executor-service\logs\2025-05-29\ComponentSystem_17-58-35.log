2025-05-29 17:58:35 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_17-58-35.log
2025-05-29 17:58:35 - ComponentSystem - INFO - [get_component_manager:1387] Creating new global ComponentManager instance
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 17:58:35 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_component_modules:1326] Discovering component modules
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_component_modules:1342] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 17:58:35 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.alter_metadata_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.api_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.api_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.combine_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.combine_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.combine_text_component_new
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.convert_script_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.data_to_dataframe_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.doc_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.doc_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.gmail_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.gmail_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.gmail_tracker_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.id_generator_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.id_generator_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.merge_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 17:58:36 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.merge_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.message_to_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.message_to_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.select_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.select_data_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.split_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.split_text_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1352] Importing component module: app.components.text_analysis_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1356] Successfully imported component module: app.components.text_analysis_component
2025-05-29 17:58:36 - ComponentSystem - INFO - [discover_component_modules:1365] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 17:58:36 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 17:58:42 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 17:58:42 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 17:58:42 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 17:58:42 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=339, TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854
2025-05-29 17:58:42 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "correlation_id": "7a0e8c4f-9266-44e8-812d-f3325d91c22d"
}
2025-05-29 17:58:42 - ComponentSystem - INFO - [_process_message:713] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Executing tool MergeDataComponent for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854
2025-05-29 17:58:42 - ComponentSystem - INFO - [_process_message:717] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Tool MergeDataComponent executed successfully for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854
2025-05-29 17:58:42 - ComponentSystem - INFO - [_send_result:1005] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Preparing to send result for component ApiRequestNode, RequestID=7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:244] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:247] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d]   Bootstrap Servers: **************:9092
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:248] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:252] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d]   Request Timeout: 60000ms
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:255] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 17:58:42 - ComponentSystem - INFO - [get_producer:259] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 17:58:44 - ComponentSystem - INFO - [get_producer:266] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 17:58:44 - ComponentSystem - INFO - [_send_result:1038] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Result has error status for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 17:58:44 - ComponentSystem - INFO - [_send_result:1078] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sending Kafka response: RequestID=7a321b15-3271-4842-bad7-8266c74a7d43, Response={
  "request_id": "7a321b15-3271-4842-bad7-8266c74a7d43",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748521724.7531345,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 17:58:45 - ComponentSystem - INFO - [_send_result:1087] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Sent result for component ApiRequestNode to topic node_results for RequestID=7a321b15-3271-4842-bad7-8266c74a7d43
2025-05-29 17:58:45 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Successfully committed offset 340 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854
2025-05-29 17:58:45 - ComponentSystem - INFO - [_process_message:936] [ReqID:7a321b15-3271-4842-bad7-8266c74a7d43] [CorrID:7a0e8c4f-9266-44e8-812d-f3325d91c22d] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=339, TaskID=ApiRequestNode-node-execution-request-0-339-1748521722.8461854
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=340, TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "2",
    "separator": ",",
    "input_1": "\"hello\":\"markrint\"",
    "input_2": "\"new_hello\":\"New_markrint\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool CombineTextComponent for RequestID=2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c, TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:717] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool CombineTextComponent executed successfully for RequestID=2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c, TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153
2025-05-29 17:59:49 - ComponentSystem - INFO - [_send_result:1005] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Preparing to send result for component ApiRequestNode, RequestID=2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ComponentSystem - INFO - [_send_result:1078] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sending Kafka response: RequestID=2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c, Response={
  "request_id": "2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748521789.3205016,
  "result": {
    "request_id": "2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c",
    "result": {
      "status": "success",
      "result": "{\"merge_text\":\"helloe\",\"hello\":\"markrint\",\"new_hello\":\"New_markrint\"}"
    }
  },
  "error": null
}
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=341, TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "ed88ea01-bdeb-498d-ae87-c4470fd24846",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:713] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool ApiRequestNode for RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846, TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886
2025-05-29 17:59:49 - ComponentSystem - INFO - [_send_result:1087] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sent result for component ApiRequestNode to topic node_results for RequestID=2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c
2025-05-29 17:59:49 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully committed offset 341 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153
2025-05-29 17:59:49 - ComponentSystem - INFO - [_process_message:936] [ReqID:2dd5fb6c-8f5e-4e40-850d-2f4aaa809e6c] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=340, TaskID=ApiRequestNode-node-execution-request-0-340-1748521789.291153
2025-05-29 17:59:50 - ComponentSystem - INFO - [_process_message:717] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool ApiRequestNode executed successfully for RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846, TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886
2025-05-29 17:59:50 - ComponentSystem - INFO - [_send_result:1005] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Preparing to send result for component ApiRequestNode, RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ComponentSystem - INFO - [_send_result:1078] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sending Kafka response: RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846, Response={
  "request_id": "ed88ea01-bdeb-498d-ae87-c4470fd24846",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748521790.235906,
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "ed88ea01-bdeb-498d-ae87-c4470fd24846",
    "response": {
      "result": "1748521789747-3573702345602",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:29:49 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-At2G18Fo1BORqGP69J6pfvEj7hY\"",
        "X-Response-Time": "0.72641ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=ihvBiP6jHrcxgiTIc6JAbRcFe0TuocUpxAWOjjI%2F7YJBRQ1BladlQwQR7hawk4%2FN0Go4CUDjEmRbzz9hru826caOdK%2BGjT5Sba52c1ApPkj8in9JowU%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9475ffe018be1e71-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "error": null
}
2025-05-29 17:59:50 - ComponentSystem - INFO - [_send_result:1087] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sent result for component ApiRequestNode to topic node_results for RequestID=ed88ea01-bdeb-498d-ae87-c4470fd24846
2025-05-29 17:59:50 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully committed offset 342 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886
2025-05-29 17:59:50 - ComponentSystem - INFO - [_process_message:936] [ReqID:ed88ea01-bdeb-498d-ae87-c4470fd24846] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=341, TaskID=ApiRequestNode-node-execution-request-0-341-1748521789.2931886
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=342, TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://www.postb.in/1748520220048-3774701473303",
    "method": "POST",
    "query_params": null,
    "headers": null,
    "body": {
      "value": "{\"technical\":\"12\"}"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "d8efe309-1471-4867-960e-a6d88e5c41bd",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:713] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool ApiRequestNode for RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd, TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=343, TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571, Payload={
  "tool_name": "MergeDataComponent",
  "tool_parameters": {
    "main_input": "{\"merge_text\":\"helloe\"",
    "num_additional_inputs": "1",
    "merge_strategy": "Deep Merge",
    "input_1": {
      "media": "help"
    },
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "421f02cc-5aab-4876-a4a9-ba023acf34e9",
  "correlation_id": "632d9bef-8033-48f6-9f33-6339b6f264da"
}
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:713] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Executing tool MergeDataComponent for RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9, TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:717] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool MergeDataComponent executed successfully for RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9, TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_send_result:1005] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Preparing to send result for component ApiRequestNode, RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ComponentSystem - INFO - [_send_result:1038] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Result has error status for RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9: Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict).
2025-05-29 17:59:57 - ComponentSystem - INFO - [_send_result:1078] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sending Kafka response: RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9, Response={
  "request_id": "421f02cc-5aab-4876-a4a9-ba023acf34e9",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748521797.3738825,
  "result": null,
  "error": "Cannot merge data of types str and dict for input input_1. All inputs must be of the same type (list or dict)."
}
2025-05-29 17:59:57 - ComponentSystem - INFO - [_send_result:1087] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sent result for component ApiRequestNode to topic node_results for RequestID=421f02cc-5aab-4876-a4a9-ba023acf34e9
2025-05-29 17:59:57 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully committed offset 344 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571
2025-05-29 17:59:57 - ComponentSystem - INFO - [_process_message:936] [ReqID:421f02cc-5aab-4876-a4a9-ba023acf34e9] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=343, TaskID=ApiRequestNode-node-execution-request-0-343-1748521797.354571
2025-05-29 17:59:58 - ComponentSystem - INFO - [_process_message:717] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Tool ApiRequestNode executed successfully for RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd, TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571
2025-05-29 17:59:58 - ComponentSystem - INFO - [_send_result:1005] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Preparing to send result for component ApiRequestNode, RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ComponentSystem - INFO - [_send_result:1078] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sending Kafka response: RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd, Response={
  "request_id": "d8efe309-1471-4867-960e-a6d88e5c41bd",
  "component_type": "ApiRequestNode",
  "status": "success",
  "timestamp": 1748521798.0661905,
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "d8efe309-1471-4867-960e-a6d88e5c41bd",
    "response": {
      "result": "1748521797587-9642807936761",
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 12:29:57 GMT",
        "Content-Type": "text/plain; charset=utf-8",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Etag": "W/\"1b-ih666MxzFe3W0bHng253rPSegVI\"",
        "X-Response-Time": "0.64883ms",
        "Via": "1.1 google",
        "Cf-Cache-Status": "DYNAMIC",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=yQYAQRMzh4bVY%2FrAUKsRdvpTyFtVIFERm%2Buso03r5zlIOdapf7jlfB%2BpKpZ1Pc5dCsjUoohxHJ4S88Oqld0EkGeCw%2B64aev6hXEwMpvXjHoURf5ZIA0%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "947600111962e112-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://www.postb.in/1748520220048-3774701473303",
      "method": "POST"
    }
  },
  "error": null
}
2025-05-29 17:59:58 - ComponentSystem - INFO - [_send_result:1087] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Sent result for component ApiRequestNode to topic node_results for RequestID=d8efe309-1471-4867-960e-a6d88e5c41bd
2025-05-29 17:59:58 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Successfully committed offset 343 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571
2025-05-29 17:59:58 - ComponentSystem - INFO - [_process_message:936] [ReqID:d8efe309-1471-4867-960e-a6d88e5c41bd] [CorrID:632d9bef-8033-48f6-9f33-6339b6f264da] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=342, TaskID=ApiRequestNode-node-execution-request-0-342-1748521797.354571
2025-05-29 18:03:48 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=344, TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878
2025-05-29 18:03:48 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "mine",
    "num_additional_inputs": "1",
    "separator": null,
    "input_1": "hello",
    "input_2": null,
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "1a31f1e9-9a7f-470a-bd24-ca525063aaa9",
  "correlation_id": "1669f02d-1670-49d2-b3fe-67e3f2deb5a3"
}
2025-05-29 18:03:48 - ComponentSystem - INFO - [_process_message:713] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Executing tool CombineTextComponent for RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9, TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878
2025-05-29 18:03:48 - ComponentSystem - INFO - [_process_message:717] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Tool CombineTextComponent executed successfully for RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9, TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878
2025-05-29 18:03:48 - ComponentSystem - INFO - [_send_result:1005] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Preparing to send result for component ApiRequestNode, RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:48 - ComponentSystem - INFO - [_send_result:1038] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Result has error status for RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9: Error combining text for request_id 1a31f1e9-9a7f-470a-bd24-ca525063aaa9: 'NoneType' object has no attribute 'replace'
2025-05-29 18:03:48 - ComponentSystem - INFO - [_send_result:1078] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Sending Kafka response: RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9, Response={
  "request_id": "1a31f1e9-9a7f-470a-bd24-ca525063aaa9",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748522028.5466058,
  "result": null,
  "error": "Error combining text for request_id 1a31f1e9-9a7f-470a-bd24-ca525063aaa9: 'NoneType' object has no attribute 'replace'"
}
2025-05-29 18:03:48 - ComponentSystem - INFO - [_send_result:1087] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Sent result for component ApiRequestNode to topic node_results for RequestID=1a31f1e9-9a7f-470a-bd24-ca525063aaa9
2025-05-29 18:03:49 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Successfully committed offset 345 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878
2025-05-29 18:03:49 - ComponentSystem - INFO - [_process_message:936] [ReqID:1a31f1e9-9a7f-470a-bd24-ca525063aaa9] [CorrID:1669f02d-1670-49d2-b3fe-67e3f2deb5a3] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=344, TaskID=ApiRequestNode-node-execution-request-0-344-1748522028.4841878
2025-05-29 18:04:31 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-05-29 18:04:31 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-05-29 18:04:31 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-05-29 18:04:31 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
