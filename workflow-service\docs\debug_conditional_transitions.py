"""
Debug script to examine exactly what transitions are created for conditional nodes.
"""

from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
import json

def debug_conditional_transitions():
    """Debug what transitions are created for conditional workflows."""

    # Test workflow with conditional node
    workflow_data = {
        "nodes": [
            {
                "id": "start-1",
                "data": {
                    "type": "StartNode",
                    "originalType": "StartNode",
                    "definition": {"name": "StartNode"}
                }
            },
            {
                "id": "text-1",
                "data": {
                    "type": "TextComponent",
                    "definition": {"name": "TextComponent"}
                }
            },
            {
                "id": "conditional-1",
                "data": {
                    "type": "ConditionalNode",
                    "definition": {
                        "name": "ConditionalNode",
                        "num_conditions": 2,
                        "condition_1_source": "node_output",
                        "condition_1_operator": "equals",
                        "condition_1_expected_value": "success"
                    }
                }
            },
            {
                "id": "output-1",
                "data": {
                    "type": "OutputNode",
                    "originalType": "OutputNode",
                    "definition": {"name": "OutputNode"}
                }
            },
            {
                "id": "output-2",
                "data": {
                    "type": "OutputNode",
                    "originalType": "OutputNode",
                    "definition": {"name": "OutputNode"}
                }
            }
        ],
        "edges": [
            {"id": "e1", "source": "start-1", "target": "text-1"},
            {"id": "e2", "source": "text-1", "target": "conditional-1"},
            {"id": "e3", "source": "conditional-1", "target": "output-1", "sourceHandle": "condition_1_output"},
            {"id": "e4", "source": "conditional-1", "target": "output-2", "sourceHandle": "condition_2_output"}
        ],
        "mcp_configs": []
    }

    # Convert to transition schema
    print("🔍 Converting workflow to transition schema...")
    transition_schema = convert_workflow_to_transition_schema(workflow_data)

    print("\n📋 NODES in transition schema:")
    for i, node in enumerate(transition_schema["nodes"]):
        print(f"  {i+1}. {node['id']}")

    print(f"\n🔄 TRANSITIONS in transition schema ({len(transition_schema['transitions'])} total):")
    for i, transition in enumerate(transition_schema["transitions"]):
        print(f"  {i+1}. {transition['id']} (sequence: {transition['sequence']})")
        if "conditional_routing" in transition:
            print(f"     ✅ Has conditional_routing with {len(transition['conditional_routing'].get('cases', []))} cases")
        else:
            print(f"     ❌ No conditional_routing")

    print("\n🔍 DETAILED TRANSITION ANALYSIS:")
    for transition in transition_schema["transitions"]:
        print(f"\n--- {transition['id']} ---")
        print(f"  Node ID: {transition['node_info']['node_id']}")
        print(f"  Execution Type: {transition['execution_type']}")
        print(f"  End: {transition['end']}")

        if "conditional_routing" in transition:
            print(f"  🎯 CONDITIONAL ROUTING:")
            cases = transition['conditional_routing'].get('cases', [])
            print(f"    Cases: {len(cases)}")
            for j, case in enumerate(cases):
                print(f"      Case {j+1}: {case.get('condition', {})} -> {case.get('next_transition', 'N/A')}")

        print(f"  Output Data ({len(transition['node_info']['output_data'])} outputs):")
        for output in transition['node_info']['output_data']:
            print(f"    -> {output.get('to_transition_id', 'N/A')} (target: {output.get('target_node_id', 'N/A')})")

    # Check for any conditional node transitions
    conditional_transitions = [t for t in transition_schema["transitions"] if "conditional" in t["id"]]
    if conditional_transitions:
        print(f"\n❌ PROBLEM: Found {len(conditional_transitions)} conditional node transitions:")
        for ct in conditional_transitions:
            print(f"  - {ct['id']}")
    else:
        print(f"\n✅ GOOD: No separate conditional node transitions found")

    print(f"\n📊 SUMMARY:")
    print(f"  Total nodes: {len(transition_schema['nodes'])}")
    print(f"  Total transitions: {len(transition_schema['transitions'])}")
    print(f"  Conditional transitions: {len(conditional_transitions)}")

    # Save full schema for inspection
    with open("debug_transition_schema.json", "w") as f:
        json.dump(transition_schema, f, indent=2)
    print(f"\n💾 Full schema saved to debug_transition_schema.json")

if __name__ == "__main__":
    debug_conditional_transitions()
